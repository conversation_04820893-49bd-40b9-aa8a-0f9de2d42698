"""
Session Management Utility Functions

This module provides reusable session management utilities
to reduce code duplication and improve maintainability.
"""

from typing import Any, Dict

from fastapi import HTTPException, status

from ..models.sessions_manager import (
    get_session_attempted_questions_count,
    get_session_details,
    get_session_score,
    get_session_user_id,
)
from .logger import warning


def validate_session_ownership(session_code: str, user_id: str) -> bool:
    """
    Validate that a user owns a session.

    Args:
        session_code: Session code to validate
        user_id: User ID to check ownership

    Returns:
        True if user owns the session

    Raises:
        HTTPException: If validation fails
    """
    session_user_id = get_session_user_id(session_code)

    if not session_user_id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")

    if session_user_id != user_id:
        warning(f"Session ownership mismatch: session belongs to {session_user_id}, request from {user_id}")
        # Don't raise an exception here, just log the warning
        # The calling code can decide how to handle this

    return session_user_id == user_id


def get_session_remaining_time(session_code: str) -> int:
    """
    Calculate remaining time for a session in seconds.

    Args:
        session_code: Session code to check

    Returns:
        Remaining time in seconds
    """
    session_details = get_session_details(session_code)

    if not session_details:
        return 0

    # This is a simplified calculation - actual implementation would need
    # to consider elapsed time since session started.
    return session_details.get("remaining_time_seconds", 0)


def is_session_valid(session_code: str) -> bool:
    """
    Check if a session is valid and active.

    Args:
        session_code: Session code to validate

    Returns:
        True if session is valid
    """
    session_details = get_session_details(session_code)

    if not session_details:
        return False

    # Check if session is completed
    if session_details.get("is_completed", False):
        return False

    # Add more validation logic as needed
    return True


def get_session_questions_data(session_code: str) -> Dict[str, Any]:
    """
    Get comprehensive session and questions data.

    Args:
        session_code: Session code to lookup

    Returns:
        Dictionary containing session and question data
    """
    session_details = get_session_details(session_code)

    if not session_details:
        return {}

    return {
        "session_details": session_details,
        "current_score": get_session_score(session_code),
        "attempted_questions_count": get_session_attempted_questions_count(session_code),
        "remaining_time_seconds": get_session_remaining_time(session_code),
        "is_valid": is_session_valid(session_code),
    }
