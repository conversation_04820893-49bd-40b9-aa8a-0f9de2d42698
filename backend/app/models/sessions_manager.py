"""
This module provides sessions-related database operations.
"""

import asyncio
import random
from typing import Any, Dict, List, Optional, Tuple, Union

import psycopg2
import psycopg2.extras
from dotenv import load_dotenv
from fastapi import HTTPException, status

from ..api.middlewares.hashid_middleware import hash_ids_in_response
from ..config.db_config import DATABASE_CONFIG
from ..models.assessment_manager import (
    calculate_total_score_for_assessment,
    get_performance_level_with_correct_total,
    get_session_and_assessment_details_by_code,
)
from ..utils.api_response import (
    error_response,
    raise_http_exception,
    success_response,
)
from ..utils.db_utils import execute_query, get_single_value
from ..utils.hashid_utils import decode_session_code
from ..utils.logger import (
    debug,
    error,
    info,
    warning,
)

load_dotenv()


def build_session_filter_clauses(status_filter: Optional[str]) -> tuple[str, str]:
    """Build WHERE clauses for session filtering."""
    if status_filter == "pending":
        clause = "WHERE (s.status = 'pending' OR s.status = 'created' OR s.status IS NULL)"
        return clause, clause
    elif status_filter == "completed":
        clause = "WHERE (s.status = 'completed' OR s.status = 'finished')"
        return clause, clause
    else:
        # If status_filter is 'all' or None, no WHERE clause needed
        return "", ""


def get_sessions_count(cur, count_where_clause: str) -> int:
    """Get total count of sessions based on filter."""
    count_query = f"""
        SELECT COUNT(*)
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
        {count_where_clause}
    """
    cur.execute(count_query)
    return cur.fetchone()[0]


def get_sessions_data(cur, where_clause: str, limit: int, offset: int) -> list:
    """Get paginated sessions data."""
    main_query = f"""
        SELECT s.id, s.code as session_code, u.external_id as username,
               s.assessment_id, s.score, s.status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name,
               CASE
                   WHEN a.question_selection_mode = 'fixed' THEN
                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                   ELSE
                       (SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        WHERE ua.session_id = s.id)
               END as total_questions
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
        {where_clause}
        ORDER BY s.created_at DESC
        LIMIT %s OFFSET %s
    """
    cur.execute(main_query, (limit, offset))
    return [dict(row) for row in cur.fetchall()]


def get_single_session_data(cur, where_clause: str, params: Tuple) -> Optional[Dict]:
    """
    Builds and executes the main session query with a specific WHERE clause
    and parameters, returning a single result as a dictionary.

    This function combines the query definition and execution logic.

    Args:
        cur: The database cursor.
        where_clause: The SQL WHERE clause to filter the results (e.g., "WHERE s.id = %s").
        params: A tuple of parameters to be safely passed to the query.

    Returns:
        A dictionary representing the session row, or None if not found.
    """
    # The base query is now defined directly inside this function
    main_query = """
        SELECT s.id, s.code as session_code, u.external_id as username,
               s.assessment_id, s.score, s.status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name,
               CASE
                   WHEN a.question_selection_mode = 'fixed' THEN
                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                   ELSE
                       (SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        WHERE ua.session_id = s.id)
               END as total_questions
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
    """

    full_query = f"{main_query} {where_clause}"

    cur.execute(full_query, params)
    session = cur.fetchone()

    return dict(session) if session else None


def generate_unique_session_code(cur) -> str:
    """Generate a unique session code."""
    for _ in range(10):  # Max 10 attempts
        session_code = str(random.randint(100000, 999999)).zfill(6)
        cur.execute("SELECT id FROM sessions WHERE code = %s", (session_code,))
        if not cur.fetchone():
            return session_code

    raise Exception("Could not generate a unique session code after 10 attempts.")


def create_session_in_db(cur, session_code: str, user_internal_id: int, assessment_id: int) -> int:
    """Create session in database and return session ID."""
    cur.execute(
        """INSERT INTO sessions (code, user_id, assessment_id, status, created_at)
           VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
           RETURNING id""",
        (session_code, user_internal_id, assessment_id, "pending"),
    )
    return cur.fetchone()["id"]


def find_or_create_user_by_details(cur, display_name: str, email: str, external_id: str):
    """
    Finds a user by their unique email or creates a new one if not found.
    This is a robust "upsert" (update/insert) operation.

    Returns:
        int: The internal database ID of the user (either existing or newly created).
    """
    # First, try to find the user by their unique email.
    cur.execute("SELECT id FROM users WHERE email = %s", (email,))
    user = cur.fetchone()
    if user:
        return user["id"]

    # If the user doesn't exist, create a new one with all required fields.
    cur.execute(
        """
        INSERT INTO users (display_name, email, external_id, created_at, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
        """,
        (display_name, email, external_id),
    )
    new_user = cur.fetchone()
    if not new_user:
        # This would indicate a serious database issue.
        raise Exception(f"Failed to create user with email {email}")
    return new_user["id"]


def validate_assessment_exists(cur, assessment_id: int):
    """Validate that assessment exists in database."""
    cur.execute("SELECT id, name FROM assessments WHERE id = %s", (assessment_id,))
    if not cur.fetchone():
        raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found.")


def handle_completed_session(session_details: dict):
    """Handle already completed session."""
    debug("Session already completed, returning existing data")

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                "SELECT score, completed_at FROM sessions WHERE id = %s",
                (session_details["session_id"],),
            )
            result = cur.fetchone()
            if result:
                data = {
                    "session_id": session_details["session_id"],
                    "obtained_score": result["score"] or 0,
                    "status": "completed",
                    "message": "Session was already completed",
                }
                hashed_data = hash_ids_in_response(data)
                return success_response(data=hashed_data, message="Session was already completed")

    return success_response(
        data={"status": "completed", "message": "Session was already completed"},
        message="Session was already completed",
    )


def handle_expired_session(session_details: dict):
    """Handle expired session by completing it."""
    debug("Session expired, but attempting to complete anyway")

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                """UPDATE sessions
                   SET status = 'completed', completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                   WHERE id = %s
                   RETURNING id""",
                (session_details["session_id"],),
            )
            result = cur.fetchone()
            if result:
                conn.commit()
                debug(f"Successfully completed expired session {session_details['session_id']}")
                return success_response(
                    data={"status": "completed", "message": "Expired session completed"},
                    message="Session completed successfully",
                )


def calculate_session_scores(session_details: dict) -> tuple[float, float, str]:
    """Calculate obtained score, total possible score, and performance level."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            # Calculate obtained score from user_answers
            cur.execute(
                "SELECT COALESCE(SUM(score), 0) as obtained_score FROM user_answers WHERE session_id = %s",
                (session_details["session_id"],),
            )
            score_result = cur.fetchone()
            obtained_score = score_result["obtained_score"] if score_result else 0

            # Calculate correct total possible score based on assessment mode
            assessment_id = session_details["assessment_id"]
            session_id = session_details["session_id"]
            total_possible_score = calculate_total_score_for_assessment(assessment_id, session_id)

            # Calculate performance level with correct total score
            performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_id)

            return obtained_score, total_possible_score, performance_level


def complete_session_in_db(session_details: dict, obtained_score: float):
    """Complete the session in database."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                """UPDATE sessions
                   SET status = 'completed',
                       score = %s,
                       completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                   WHERE id = %s AND status = 'in_progress'
                   RETURNING id""",
                (obtained_score, session_details["session_id"]),
            )

            updated_session = cur.fetchone()
            if not updated_session:
                raise_http_exception(status_code=400, detail="Session could not be completed.")

            conn.commit()


# --- Helper Function to avoid repeating the main query ---


def _get_session_base_query() -> str:
    """Returns the base SELECT statement for session details to keep queries DRY."""
    return """
        SELECT s.id, s.code as session_code, s.user_id, u.external_id as username, u.display_name,
               s.assessment_id, s.score, s.status as session_status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name, a.is_final, a.question_selection_mode, a.duration_minutes
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
    """


# --- Functions for specific data retrieval and updates ---


def get_session_for_start_or_validation(cur, session_code: str) -> Optional[Dict]:
    """
    Gets comprehensive session details by its 6-digit code.
    Used for validation and starting a session. Joins sessions, users, and assessments.
    """
    base_query = _get_session_base_query()
    query = f"{base_query} WHERE s.code = %s"
    cur.execute(query, (session_code,))
    session = cur.fetchone()
    # Note: `get_session_and_assessment_details_by_code` is now replaced by this.
    return dict(session) if session else None


def start_session_in_db(cur, session_id: int) -> int:
    """
    Updates a session's status to 'in_progress' and sets the start time.
    Returns the number of rows affected (should be 1 on success, 0 on failure).
    """
    query = """
        UPDATE sessions
        SET status = 'in_progress', started_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE id = %s AND status = 'pending'
    """
    cur.execute(query, (session_id,))
    return cur.rowcount


def get_completed_session_for_results(cur, session_identifier: str) -> Optional[Dict]:
    """
    Gets a completed session's details by its 6-digit code or numeric ID.
    """
    base_query = _get_session_base_query()

    if len(session_identifier) == 6 and session_identifier.isdigit():
        # It's a 6-digit session code
        where_clause = "WHERE s.code = %s AND s.status = 'completed'"
        params = (session_identifier,)
    elif session_identifier.isdigit():
        # It's a numeric ID
        where_clause = "WHERE s.id = %s AND s.status = 'completed'"
        params = (int(session_identifier),)
    else:
        # It's a hash, decode it first (logic remains in the endpoint)
        # This function assumes the ID is already decoded if it's not a code/numeric ID.
        where_clause = "WHERE s.id = %s AND s.status = 'completed'"
        # You would decode the hash *before* calling this function.
        # For simplicity, we assume the endpoint will handle decoding and pass a numeric ID.
        return None  # Or handle decoded ID if you pass it in

    query = f"{base_query} {where_clause}"
    cur.execute(query, params)
    session = cur.fetchone()
    return dict(session) if session else None


def get_session_answers_for_results(cur, session_internal_id: int) -> List[Dict]:
    """
    Gets all questions, user answers, and correct answers for a given session.
    """
    query = """
        SELECT ua.question_id, ua.user_answer, ua.is_correct, ua.score,
               q.question, q.options, q.answer as correct_answer_key, q.level
        FROM user_answers ua
        JOIN questions q ON ua.question_id = q.que_id
        WHERE ua.session_id = %s
        ORDER BY ua.created_at
    """
    cur.execute(query, (session_internal_id,))
    return [dict(row) for row in cur.fetchall()]


def get_session_user_details(cur, session_code: str) -> Optional[Dict]:
    """

    Gets user and assessment details for a given session code in a single query.
    """
    query = """
        SELECT
            u.external_id,
            u.display_name,
            a.id as assessment_id,
            a.name as assessment_name,
            a.is_final,
            s.status as session_status
        FROM sessions s
        JOIN users u ON s.user_id = u.id
        JOIN assessments a ON s.assessment_id = a.id
        WHERE s.code = %s
    """
    cur.execute(query, (session_code,))
    result = cur.fetchone()
    return dict(result) if result else None


def get_user_id_by_email(cur, email: str) -> Optional[int]:
    """
    Fetches a user's internal integer ID from their email address.

    Args:
        cur: The database cursor.
        email: The user's email address.

    Returns:
        The integer user ID, or None if not found.
    """
    query = "SELECT id FROM users WHERE email = %s"
    cur.execute(query, (email,))
    result = cur.fetchone()
    return result["id"] if result else None


def get_sessions_by_user_id(cur, user_id: int) -> List[Dict]:
    """
    Fetches all sessions associated with a specific user ID, ordered by creation date.
    This reuses the main session query for consistency.

    Args:
        cur: The database cursor.
        user_id: The internal integer ID of the user.

    Returns:
        A list of dictionaries, where each dictionary represents a session.
    """
    base_query = _get_session_base_query()
    # Append the specific filter and ordering for this use case
    full_query = f"{base_query} WHERE s.user_id = %s ORDER BY s.created_at DESC"
    cur.execute(full_query, (user_id,))
    return [dict(row) for row in cur.fetchall()]


def db_get_user_by_email(cur, email: str) -> Optional[Dict]:
    """
    Fetches a full user record from the database by email.

    Returns:
        A dictionary representing the user, or None if not found.
    """
    query = "SELECT id, external_id, email, display_name FROM users WHERE email = %s"
    cur.execute(query, (email,))
    user = cur.fetchone()
    return dict(user) if user else None


def db_get_user_by_external_id(cur, external_id: str) -> Optional[Dict]:
    """
    Fetches a full user record from the database by their external_id.

    Returns:
        A dictionary representing the user, or None if not found.
    """
    query = "SELECT id, external_id, email, display_name FROM users WHERE external_id = %s"
    cur.execute(query, (external_id,))
    user = cur.fetchone()
    return dict(user) if user else None


def db_update_user_email(cur, user_id: int, email: str) -> int:
    """
    Executes the query to update a user's email if it is currently null or empty.

    Returns:
        The number of rows affected (0 or 1).
    """
    query = "UPDATE users SET email = %s WHERE id = %s AND (email IS NULL OR email = '')"
    cur.execute(query, (email, user_id))
    return cur.rowcount


def db_create_user(cur, external_id: str, email: str, display_name: str) -> int:
    """
    Executes the query to insert a new user and returns their new internal ID.

    Returns:
        The integer ID of the newly created user.
    """
    query = "INSERT INTO users (external_id, email, display_name) VALUES (%s, %s, %s) RETURNING id"
    cur.execute(query, (external_id, email, display_name))
    new_user_id = cur.fetchone()[0]
    return new_user_id


def _find_session_by_code(cur, session_code: str) -> tuple:
    """Find session by code and return session details."""
    cur.execute(
        "SELECT id, user_id, assessment_id FROM sessions WHERE code = %s",
        (session_code,),
    )
    return cur.fetchone()


def _validate_session_ownership(
    session_code: str, session_user_id: int, session_assessment_id: int, internal_user_id: int, assessment_id: int
):
    """Validate that session belongs to the correct user and assessment."""
    if session_user_id != internal_user_id or session_assessment_id != assessment_id:
        error(
            f"Session code {session_code} mismatch: user ({session_user_id} vs {internal_user_id}) "
            f"or assessment ({session_assessment_id} vs {assessment_id})"
        )
        raise HTTPException(
            status_code=403,
            detail="Session code mismatch or unauthorized.",
        )


def _update_session_to_in_progress(cur, conn, session_db_id: int) -> int:
    """Update session status to in_progress and return session ID."""
    cur.execute(
        """UPDATE sessions SET status = 'in_progress',
           started_at = COALESCE(started_at, CURRENT_TIMESTAMP AT TIME ZONE 'UTC')
           WHERE id = %s AND (status = 'pending' OR status = 'in_progress') RETURNING id""",
        (session_db_id,),
    )
    updated_session = cur.fetchone()
    conn.commit()

    if updated_session:
        return updated_session[0]
    else:
        # Session might be completed or expired, return existing id anyway
        cur.execute("SELECT id FROM sessions WHERE id = %s", (session_db_id,))
        return cur.fetchone()[0]


def get_or_create_session(user_id_external: str, assessment_id: int, session_code: str):
    """
    Get or create a session for the user and assessment.
    If session exists by code, it updates its status to 'in_progress' if 'pending'.
    """
    conn = None
    try:
        internal_user_id = get_or_create_user(user_id_external)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if session exists for this code
                session_result = _find_session_by_code(cur, session_code)

                if session_result:
                    session_db_id, session_user_id, session_assessment_id = session_result

                    # Validate session ownership
                    _validate_session_ownership(
                        session_code, session_user_id, session_assessment_id, internal_user_id, assessment_id
                    )

                    # Update session to in_progress
                    return _update_session_to_in_progress(cur, conn, session_db_id)

                # Session code does not exist
                error(f"Session code {session_code} not found. Cannot create on-the-fly in get_or_create_session.")
                return error_response(
                    message="Invalid session code.",
                    code=status.HTTP_404_NOT_FOUND,
                    error_type="NotFound",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error in get_or_create_session: {e}", exc_info=True)
        if conn:
            conn.rollback()
        return error_response(
            message="Error processing session.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


def expire_sessions():
    """
    Background task to mark sessions as expired if past completed_at and not completed.
    This function runs periodically to check for expired sessions.
    """
    try:
        debug("Running session expiry check...")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Find sessions that are past their completed_at time and still in progress
                cur.execute(
                    """UPDATE sessions
                       SET status = 'expired'
                       WHERE status = 'in_progress'
                       AND completed_at IS NOT NULL
                       AND completed_at < CURRENT_TIMESTAMP
                       RETURNING id, code""",
                )
                expired_sessions = cur.fetchall()

                if expired_sessions:
                    info(f"Expired {len(expired_sessions)} sessions: {[s['code'] for s in expired_sessions]}")
                else:
                    debug("No sessions to expire")

                conn.commit()

    except Exception as e:
        error(f"Error during session expiry check: {str(e)}", exc_info=True)


async def periodic_session_expiry():
    """
    Periodic task to run session expiry checks every 5 minutes.
    """
    while True:
        try:
            expire_sessions()
            # Wait 5 minutes before next check
            await asyncio.sleep(300)
        except Exception as e:
            error(f"Error in periodic session expiry: {str(e)}")
            # Wait 1 minute before retrying on error
            await asyncio.sleep(60)


def validate_session_for_question(session_code: str) -> tuple[str, dict]:
    """Validate session and return decoded session code and session details."""
    decoded_session_code = validate_session_code_format(session_code)
    session_details = get_session_and_assessment_details_by_code(decoded_session_code)

    if not session_details:
        return None, error_response(
            message="Invalid or expired session code.",
            code=status.HTTP_404_NOT_FOUND,
            error_type="NotFound",
        )

    return decoded_session_code, session_details


def get_session_response_data(
    session_details: dict,
    is_correct: bool,
    correct_answer_key: str,
    correct_answer_value: str,
    normalized_session_code: str,
) -> dict:
    """Get response data for the session."""
    current_score = get_session_score(normalized_session_code)
    remaining_time_seconds = session_details.get("remaining_time_seconds", 0)

    attempted_questions_count = get_single_value(
        """SELECT COUNT(*) FROM user_answers WHERE session_id = %s""", (session_details["session_id"],), default=0
    )

    question_selection_mode = get_single_value(
        "SELECT question_selection_mode FROM assessments WHERE id = %s",
        (session_details["assessment_id"],),
        default="dynamic",
    )

    return {
        "is_correct": is_correct,
        "correct_answer_key": correct_answer_key,
        "correct_answer_value": correct_answer_value,
        "current_score": current_score,
        "remaining_time_seconds": remaining_time_seconds,
        "attempted_questions_count": attempted_questions_count,
        "question_selection_mode": question_selection_mode,
    }


def validate_session_and_user(session_code: str, user_id: str) -> tuple[str, dict, str]:
    """Validate session and user, return normalized session code, session details, and validated user_id."""
    normalized_session_code = validate_session_code_format(session_code)

    session_details = get_session_details(normalized_session_code)
    if not session_details:
        raise_http_exception(status_code=404, detail="Invalid session code")

    session_user_id = get_session_user_id(normalized_session_code)
    if not session_user_id:
        raise_http_exception(status_code=404, detail="User not found for this session")

    if session_user_id != user_id:
        warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")

    return normalized_session_code, session_details, session_user_id


# from utils


def update_session_status(session_code: str, updates: Dict[str, Any]) -> bool:
    """
    Update session status in database.

    Args:
        session_code: Session code to update
        updates: Dictionary of fields to update

    Returns:
        True if update was successful
    """
    session_details = get_session_details(session_code)

    if not session_details:
        return False

    # Build update query
    set_clauses = []
    values = []

    for field, value in updates.items():
        set_clauses.append(f"{field} = %s")
        values.append(value)

    values.append(session_details["session_id"])

    query = f"""
        UPDATE sessions
        SET {', '.join(set_clauses)}, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
    """

    try:
        execute_query(query, tuple(values), commit=True)
        return True
    except Exception as e:
        error(f"Failed to update session status: {e}")
        return False


def get_session_score(session_code: str) -> float:
    """
    Calculate current score for a session.

    Args:
        session_code: Session code to check

    Returns:
        Current score
    """
    session_details = get_session_details(session_code)

    if not session_details:
        return 0.0

    query = """
        SELECT SUM(score) as total_score
        FROM user_answers
        WHERE session_id = %s
    """

    result = get_single_value(query, (session_details["session_id"],))
    return float(result) if result else 0.0


def get_session_attempted_questions_count(session_code: str) -> int:
    """
    Get the number of attempted questions for a session.

    Args:
        session_code: Session code to check

    Returns:
        Number of attempted questions
    """
    session_details = get_session_details(session_code)

    if not session_details:
        return 0

    query = """
        SELECT COUNT(*)
        FROM user_answers
        WHERE session_id = %s
    """

    return get_single_value(query, (session_details["session_id"],), default=0)


def get_session_details(session_code: str) -> Optional[Dict[str, Any]]:
    """
    Get session details from database.

    Args:
        session_code: Session code to lookup

    Returns:
        Dictionary containing session details or None if not found
    """
    normalized_code = validate_session_code_format(session_code)

    query = """
        SELECT s.id as session_id, s.user_id, s.assessment_id, s.code as session_code,
               s.started_at as start_time, s.completed_at as end_time, s.status,
               s.created_at,
               a.name as assessment_name, a.description as assessment_description,
               a.question_selection_mode, a.duration_minutes as assessment_duration,
               u.external_id as user_external_id, u.display_name as user_display_name,
               u.email as user_email
        FROM sessions s
        LEFT JOIN assessments a ON s.assessment_id = a.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.code = %s
    """

    return get_single_row(query, (normalized_code,), as_dict=True)


def get_session_user_id(session_code: str) -> Optional[str]:
    """
    Get the external user ID for a session.

    Args:
        session_code: Session code to lookup

    Returns:
        External user ID or None if not found
    """
    query = """
        SELECT u.external_id
        FROM sessions s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.code = %s
    """

    normalized_code = validate_session_code_format(session_code)
    return get_single_value(query, (normalized_code,))


def _parse_user_identifier(user_id: str) -> tuple[str, str, str]:
    """Parse user identifier and return email, username, and external_id."""
    if "@" in user_id:
        email = user_id
        username = user_id.split("@")[0]
        external_id = user_id  # Use full email as external_id to avoid conflicts
    else:
        email = None
        username = user_id
        external_id = username

    return email, username, external_id


def _find_user_by_email(cur, email: str) -> Optional[int]:
    """
    Find user by email by calling the data access layer and return the user ID.
    """
    if not email:
        return None
    # Call the data access function to get the user data
    user_data = db_get_user_by_email(cur, email)
    # Return just the ID, as per the original function's contract
    return user_data["id"] if user_data else None


def _find_user_by_external_id(cur, username: str) -> Optional[int]:
    """
    Find user by external_id by calling the data access layer and return the user ID.
    """
    # Call the data access function to get the user data
    user_data = db_get_user_by_external_id(cur, username)
    # Return just the ID, as per the original function's contract
    return user_data["id"] if user_data else None


def _update_user_email_if_needed(cur, conn, user_id: int, email: str):
    """
    Update user email if needed, handling the logic and transaction commit.
    """
    if email:
        # Call the data access function to perform the update
        rows_updated = db_update_user_email(cur, user_id, email)
        # Commit only if a change was actually made
        if rows_updated > 0:
            conn.commit()


def _create_new_user(cur, conn, external_id: str, email: str, username: str) -> int:
    """
    Create a new user by calling the data access layer and committing the transaction.
    """
    # Call the data access function to insert the user and get the new ID
    user_internal_id = db_create_user(cur, external_id, email, username)
    # Commit the transaction after the user is successfully created
    conn.commit()
    return user_internal_id


def get_or_create_user(user_id):
    """
    Get or create a user record and return the internal ID.
    If user_id is an email, extract the username part (before @) for external_id and display_name.
    First checks if a user with the same email exists, then checks by external_id.
    """
    try:
        # Parse user identifier
        email, username, external_id = _parse_user_identifier(user_id)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First check if a user with this email exists
                user_id_by_email = _find_user_by_email(cur, email)
                if user_id_by_email:
                    return user_id_by_email

                # Then check if user exists by external_id
                user_id_by_external = _find_user_by_external_id(cur, username)
                if user_id_by_external:
                    # Update email if needed
                    _update_user_email_if_needed(cur, conn, user_id_by_external, email)
                    return user_id_by_external

                # Create new user
                return _create_new_user(cur, conn, external_id, email, username)

    except Exception as e:
        error(f"Error in get_or_create_user: {e}")
        raise


def get_single_row(
    query: str,
    params: Optional[Tuple] = None,
    as_dict: bool = False,
) -> Optional[Union[Tuple, Dict]]:
    """
    Execute a query and return a single row.

    Args:
        query: SQL query string
        params: Query parameters
        as_dict: Whether to return result as dictionary

    Returns:
        Single row from the query result
    """
    cursor_factory = psycopg2.extras.DictCursor if as_dict else None
    result = execute_query(query, params, fetch_one=True, cursor_factory=cursor_factory)

    if result and as_dict:
        return dict(result)
    return result


def validate_session_code_format(session_code_input: str) -> str:
    """
    Validate and normalize session code input.

    Args:
        session_code_input: Either a 6-digit session code or an encoded hash

    Returns:
        6-digit session code string

    Raises:
        HTTPException: If session code is invalid
    """
    debug(
        f"Validating session code: '{session_code_input}' "
        f"(type: {type(session_code_input)}, len: {len(session_code_input) if session_code_input else 'None'})"
    )

    # Handle None or empty string
    if not session_code_input:
        error("Empty session code provided")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Session code cannot be empty",
        )

    # Ensure session_code_input is a string
    session_code_input = str(session_code_input)

    # Check if it's already a 6-digit code
    if session_code_input.isdigit() and len(session_code_input) == 6:
        debug(f"Session code is already 6-digit: {session_code_input}")
        return session_code_input

    # Try to decode as hash
    decoded_code = decode_session_code(session_code_input)
    if decoded_code:
        debug(f"Decoded hash '{session_code_input}' to: {decoded_code}")
        return decoded_code

    # If neither worked, raise an error
    error(f"Failed to decode session code: '{session_code_input}'")
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Invalid session code format. Must be either a 6-digit code or valid hash.",
    )
