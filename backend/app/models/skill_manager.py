"""
This module provides skill-related database operations.
"""

from typing import Dict, List, Optional, Tuple

import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

from ..config.db_config import DATABASE_CONFIG
from ..utils.logger import error, log_database_error

load_dotenv()


def validate_skills_exist(skill_ids: List[int]) -> Tuple[bool, List[int]]:
    """
    Validate that all skill IDs exist in the database.

    Args:
        skill_ids: List of skill IDs to validate

    Returns:
        Tuple of (all_exist: bool, missing_skills: List[int])
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                placeholders = ",".join(["%s"] * len(skill_ids))
                cur.execute(f"SELECT id FROM skills WHERE id IN ({placeholders})", skill_ids)
                existing_skills = [row[0] for row in cur.fetchall()]

                missing_skills = list(set(skill_ids) - set(existing_skills))
                return len(missing_skills) == 0, missing_skills

    except Exception as e:
        log_database_error("select", "skills", e, skill_ids=skill_ids)
        return False, skill_ids


def valid_skill_description(topic):
    """
    Check if a topic matches a valid skill description in the database.

    This function checks if the topic exactly matches a skill name or if it starts with a valid skill name
    followed by a timestamp (e.g., "SkillName_DD_MM_YYYY").

    Args:
        topic (str): The topic to validate

    Returns:
        bool: True if the topic matches a valid skill, False otherwise
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First try exact match
                cur.execute("SELECT COUNT(*) FROM skills WHERE name = %s", (topic,))
                count = cur.fetchone()[0]
                if count > 0:
                    return True

                # If no exact match, check if topic starts with a valid skill name
                # This handles cases where the topic is in the format "SkillName_timestamp"
                cur.execute("SELECT name FROM skills")
                skill_names = [row[0] for row in cur.fetchall()]

                for skill_name in skill_names:
                    if topic.startswith(skill_name + "_"):
                        return True

                return False
    except Exception as e:
        error(f"Error validating skill description: {e}")
        return False


def get_skill_id_by_name(skill_name: str) -> Optional[int]:
    """
    Get skill ID by skill name.

    Args:
        skill_name: Name of the skill to find

    Returns:
        Skill ID if found, None otherwise
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT id FROM skills WHERE name = %s", (skill_name,))
                result = cur.fetchone()
                return result[0] if result else None
    except Exception as e:
        error(f"Error getting skill ID by name: {e}")
        return None


def create_skill(name: str, description: str) -> Optional[Dict]:
    """
    Create a new skill in the database.

    Args:
        name: Name of the skill
        description: Description of the skill

    Returns:
        Dictionary with skill data if successful, None otherwise
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """INSERT INTO skills (name, description)
                       VALUES (%s, %s) RETURNING id, name, description""",
                    (name, description),
                )
                skill = dict(cur.fetchone())
                conn.commit()
                return skill
    except psycopg2.errors.UniqueViolation:
        raise ValueError("A skill with this name already exists")
    except Exception as e:
        error(f"Error creating skill: {str(e)}")
        return None


def get_skills_paginated(limit: int, offset: int) -> Tuple[List[Dict], int]:
    """
    Get skills with pagination and question counts.

    Args:
        limit: Maximum number of items per page
        offset: Starting position

    Returns:
        Tuple of (skills_list, total_count)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total count first
                cur.execute("SELECT COUNT(*) FROM skills")
                total = cur.fetchone()[0]

                # Get paginated results with question count (excluding timestamps)
                cur.execute(
                    """
                    SELECT s.id, s.name, s.description,
                           COUNT(q.que_id) as question_count
                    FROM skills s
                    LEFT JOIN questions q ON s.id = q.skill_id
                    GROUP BY s.id, s.name, s.description
                    ORDER BY s.name
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset),
                )
                skills = [dict(row) for row in cur.fetchall()]

                return skills, total
    except Exception as e:
        error(f"Error getting skills: {str(e)}")
        return [], 0


def get_skill_by_id(skill_id: int) -> Optional[Dict]:
    """
    Get skill details by ID with question count.

    Args:
        skill_id: ID of the skill

    Returns:
        Dictionary with skill data if found, None otherwise
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get skill details (excluding timestamps)
                cur.execute(
                    "SELECT id, name, description FROM skills WHERE id = %s",
                    (skill_id,),
                )
                skill = cur.fetchone()

                if not skill:
                    return None

                skill_dict = dict(skill)

                # Get question count for this skill
                cur.execute(
                    "SELECT COUNT(*) as question_count FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                question_count = cur.fetchone()[0]
                skill_dict["question_count"] = question_count

                return skill_dict
    except Exception as e:
        error(f"Error getting skill by ID: {str(e)}")
        return None


def get_skill_question_counts() -> List[Dict]:
    """
    Get question counts for each skill.

    Returns:
        List of dictionaries with skill_id and count
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT skill_id, COUNT(*) as count
                    FROM questions
                    GROUP BY skill_id
                    ORDER BY skill_id
                    """
                )
                return [dict(row) for row in cur.fetchall()]
    except Exception as e:
        error(f"Error getting skill question counts: {str(e)}")
        return []


def get_question_count_for_skill(skill_id: int) -> Optional[int]:
    """
    Get current question count for a specific skill.

    Args:
        skill_id: ID of the skill

    Returns:
        Question count if skill exists, None if skill not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if skill exists
                cur.execute("SELECT id FROM skills WHERE id = %s", (skill_id,))
                if not cur.fetchone():
                    return None

                # Get question count
                cur.execute(
                    "SELECT COUNT(*) as count FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                return cur.fetchone()[0]
    except Exception as e:
        error(f"Error getting question count for skill: {str(e)}")
        return None


def get_skill_questions_paginated(skill_id: int, limit: int, offset: int) -> Tuple[Optional[Dict], List[Dict], int]:
    """
    Get questions for a skill with pagination.

    Args:
        skill_id: ID of the skill
        limit: Maximum number of items per page
        offset: Starting position

    Returns:
        Tuple of (skill_info, questions_list, total_count)
        skill_info is None if skill not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the skill exists
                cur.execute("SELECT id, name FROM skills WHERE id = %s", (skill_id,))
                skill = cur.fetchone()
                if not skill:
                    return None, [], 0

                skill_dict = dict(skill)

                # Get total count of questions for this skill
                cur.execute(
                    "SELECT COUNT(*) FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                total = cur.fetchone()[0]

                # Get paginated questions (excluding timestamps)
                cur.execute(
                    """
                    SELECT que_id, topic, level, question, options, answer
                    FROM questions
                    WHERE skill_id = %s
                    ORDER BY que_id
                    LIMIT %s OFFSET %s
                    """,
                    (skill_id, limit, offset),
                )
                questions = [dict(row) for row in cur.fetchall()]

                return skill_dict, questions, total
    except Exception as e:
        error(f"Error getting skill questions: {str(e)}")
        return None, [], 0


def get_user_skill_metrics() -> List[Dict]:
    """
    Get skill-based metrics for all users.

    Returns:
        List of dictionaries with user and skill performance data
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)),
                            4
                        ) AS accuracy_ratio
                    FROM users u
                    JOIN sessions sess ON u.id = sess.user_id
                    JOIN user_answers ua ON sess.id = ua.session_id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY u.display_name, s.name
                    """
                )
                return [dict(row) for row in cur.fetchall()]
    except Exception as e:
        error(f"Error getting user skill metrics: {str(e)}")
        return []


def get_user_skill_metrics_by_email(email: str) -> Tuple[Optional[int], List[Dict]]:
    """
    Get skill-based metrics for a specific user by email.

    Args:
        email: User's email address

    Returns:
        Tuple of (user_id, metrics_list)
        user_id is None if user not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, get the user's internal ID
                cur.execute(
                    """
                    SELECT id FROM users WHERE email = %s
                    """,
                    (email,),
                )
                user_result = cur.fetchone()
                if not user_result:
                    return None, []

                user_id = user_result[0]

                # Get skill metrics for this user
                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)),
                            4
                        ) AS accuracy_ratio
                    FROM users u
                    JOIN sessions sess ON u.id = sess.user_id
                    JOIN user_answers ua ON sess.id = ua.session_id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )
                metrics = [dict(row) for row in cur.fetchall()]

                return user_id, metrics
    except Exception as e:
        error(f"Error getting user skill metrics by email: {str(e)}")
        return None, []


def get_user_skill_performance_detailed(user_id: int) -> List[Dict]:
    """
    Get detailed skill performance data for a specific user including level breakdowns.

    Args:
        user_id: ID of the user

    Returns:
        List of dictionaries with detailed skill performance data
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,

                        -- Easy level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS easy_correct,
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS easy_incorrect,

                        -- Intermediate level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS intermediate_correct,
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS intermediate_incorrect,

                        -- Advanced level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS advanced_correct,
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS advanced_incorrect

                    FROM user_answers ua
                    JOIN sessions sess ON ua.session_id = sess.id
                    JOIN users u ON sess.user_id = u.id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )
                skill_performance = [dict(row) for row in cur.fetchall()]

                # Convert numeric values to appropriate types for JSON serialization
                for skill in skill_performance:
                    skill["total_questions_answered"] = int(skill["total_questions_answered"])
                    skill["correct_answers"] = int(skill["correct_answers"])
                    skill["total_score"] = float(skill["total_score"])
                    skill["avg_score"] = float(skill["avg_score"])
                    skill["accuracy_percentage"] = float(skill["accuracy_percentage"])

                return skill_performance
    except Exception as e:
        error(f"Error getting detailed user skill performance: {str(e)}")
        return []


def get_user_skill_performance_detailed_by_email(email: str) -> Tuple[Optional[int], List[Dict]]:
    """
    Get detailed skill performance data for a specific user by email including level breakdowns.

    Args:
        email: User's email address

    Returns:
        Tuple of (user_id, performance_data)
        user_id is None if user not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, get the user's internal ID
                cur.execute("SELECT id FROM users WHERE email = %s", (email,))
                user_result = cur.fetchone()
                if not user_result:
                    return None, []

                user_id = user_result["id"]

                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,

                        -- Easy level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS easy_correct,
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS easy_incorrect,

                        -- Intermediate level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS intermediate_correct,
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS intermediate_incorrect,

                        -- Advanced level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS advanced_correct,
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS advanced_incorrect

                    FROM user_answers ua
                    JOIN sessions sess ON ua.session_id = sess.id
                    JOIN users u ON sess.user_id = u.id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )

                skills = [dict(row) for row in cur.fetchall()]
                return user_id, skills
    except Exception as e:
        error(f"Error getting detailed user skill performance by email: {str(e)}")
        return None, []
