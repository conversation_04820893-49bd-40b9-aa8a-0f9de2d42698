/* Common CSS styles for views components */

/* Custom scrollbar for overflow areas */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    rgba(6, 182, 212, 0.5),
    rgba(59, 130, 246, 0.5)
  );
  border-radius: 4px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    to bottom,
    rgba(6, 182, 212, 0.7),
    rgba(59, 130, 246, 0.7)
  );
}

/* Add some padding to the right of the content to prevent overlap with scrollbar */
.custom-scrollbar {
  padding-right: 4px;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: rgba(6, 182, 212, 0.5) rgba(31, 41, 55, 0.5); /* For Firefox */
}

/* Fade transition for error and success messages */
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.5s,
    transform 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Common height and overflow utilities */
.h-screen {
  height: 100vh;
}

.overflow-hidden {
  overflow: hidden;
}

/* Shadow glow effect */
.shadow-glow-md {
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.15);
}

/* Message display styles */
.message-success {
  @apply bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl flex items-center;
}

.message-error {
  @apply bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl flex items-center;
}

/* Loading indicator styles */
.loading-indicator {
  @apply flex justify-center items-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue;
}

/* Component-specific styles to replace inline styles */

/* Button text visibility fixes */
.btn-text-visible {
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Z-index layering */
.z-layer-high {
  position: relative;
  z-index: 1000;
}

.z-layer-low {
  position: relative;
  z-index: 1;
}

/* Font family specifications */
.font-mono-courier {
  font-family: "Courier New", Consolas, monospace;
}

/* Animated Herbit Art styles */
.herbit-char-typewriter {
  font-family: "Courier New", Consolas, monospace;
}

.char.anim-spiral {
  display: inline-block;
  opacity: 0;
  animation: spiral-reveal 0.1s ease-out forwards;
}

@keyframes spiral-reveal {
  from {
    opacity: 0;
    transform: scale(0.8) rotate(-10deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* ApexCharts custom tooltip styles */
.apexcharts-tooltip-custom {
  padding: 10px;
  background: rgba(30, 30, 30, 0.95);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #fff;
  font-size: 14px;
}

.tooltip-skill {
  font-weight: bold;
  color: #008ffb;
  margin-bottom: 8px;
  font-size: 13px;
}

.tooltip-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.tooltip-label {
  color: rgba(255, 255, 255, 0.7);
}

.tooltip-value {
  color: #fff;
  font-weight: bold;
}

.tooltip-value-white {
  color: #fff;
}

.tooltip-value-success {
  color: #10b981;
}

/* Alternative scrollbar style for assessment details */
.custom-scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    rgba(56, 152, 255, 0.5),
    rgba(123, 95, 253, 0.5)
  );
  border-radius: 3px;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    to bottom,
    rgba(56, 152, 255, 0.7),
    rgba(123, 95, 253, 0.7)
  );
}

/* Transition classes */
.fade-transition-enter-active,
.fade-transition-leave-active {
  transition: opacity 0.3s ease;
}

.fade-transition-enter-from,
.fade-transition-leave-to {
  opacity: 0;
}

/* Slide down transition for collapsible sections */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  max-height: 1000px;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

/* Callback page utility classes */
.callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-message {
  text-align: center;
  font-size: 1.2rem;
}

/* Error page shadow effect */
.shadow-error-glow {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Reusable button styles */
.btn-phantom {
  @apply bg-gradient-to-r from-phantom-blue to-phantom-indigo text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-phantom-blue/25;
}

.btn-phantom-secondary {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 text-white font-medium rounded-lg transition-all duration-200 hover:bg-white/10 hover:border-white/20 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Reusable animation classes */
.animate-ping-slow {
  animation: ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping-slow {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Scoreboard modal scrollbar variant */
.custom-scrollbar-modal {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
}

.custom-scrollbar-modal::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar-modal::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar-modal::-webkit-scrollbar-thumb {
  background-color: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

.custom-scrollbar-modal::-webkit-scrollbar-thumb:hover {
  background-color: rgba(139, 92, 246, 0.5);
}

/* Modal animation classes */
.animate-fadeIn {
  animation: fadeIn 0.2s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
