<template>
  <PhantomLayout title="Create Assessment">
    <!-- Full-width container -->
    <div class="w-full p-7 -mt-14">
      <!-- Back to Assessment List Button - Top Right -->
      <div class="flex justify-end mb-8">
        <button
          class="btn-phantom-secondary px-6 py-3 text-base"
          @click="navigation.goToAssessmentsList()"
        >
          <span class="flex items-center">
            <SvgIcon name="arrow-left" class="mr-2" />
            Back to Assessment List
          </span>
        </button>
      </div>

      <form class="space-y-12" @submit.prevent="createAssessment">
        <!-- Basic Information and Skills Selection Side by Side -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Basic Information Section -->
          <section
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8"
          >
            <h2
              class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
            >
              Basic Information
            </h2>

            <div class="space-y-6">
              <!-- Assessment Name -->
              <div>
                <label
                  for="assessmentName"
                  class="block text-white font-medium mb-2"
                  >Assessment Name</label
                >
                <input
                  id="assessmentName"
                  v-model="assessmentName"
                  name="assessmentName"
                  type="text"
                  autocomplete="off"
                  placeholder="e.g. DevOps Basics"
                  required
                  class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
                />
              </div>

              <!-- Assessment Duration -->
              <div>
                <label
                  for="assessmentDuration"
                  class="block text-white font-medium mb-2"
                  >Assessment Duration (minutes)</label
                >
                <div class="flex items-center">
                  <input
                    id="assessmentDuration"
                    v-model="assessmentDuration"
                    type="number"
                    name="assessmentDuration"
                    min="5"
                    max="180"
                    autocomplete="off"
                    class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
                    required
                  />
                </div>
                <div class="mt-1 text-xs text-white/60">
                  Set the time limit for completing this assessment
                </div>
              </div>

              <!-- Assessment Description -->
              <div>
                <label
                  for="assessmentDescription"
                  class="block text-white font-medium mb-2"
                  >Assessment Description</label
                >
                <textarea
                  id="assessmentDescription"
                  v-model="assessmentDescription"
                  name="assessmentDescription"
                  autocomplete="off"
                  placeholder="e.g. A comprehensive assessment of DevOps fundamentals including CI/CD pipelines, containerization, and infrastructure automation"
                  required
                  class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 resize-y min-h-[100px]"
                />
                <div class="mt-1 text-xs text-white/60">
                  Provide a detailed description of what this assessment covers
                  (minimum 20 characters)
                </div>
              </div>
            </div>
          </section>

          <!-- Skills Section with Question Selection Mode Below -->
          <div class="space-y-8">
            <!-- Skills Section -->
            <section
              class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 z-layer-high"
            >
              <h2
                class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
              >
                Skills Selection
              </h2>

              <div>
                <label class="block text-white font-medium mb-2"
                  >Select Skills</label
                >
                <div class="mb-2 text-xs text-white/60">
                  You can select multiple skills for this assessment
                </div>

                <div class="flex items-center space-x-2">
                  <Select
                    v-model="selectedSkillIds"
                    :disabled="isLoading"
                    multiple
                    class="w-full"
                    @update:model-value="onSkillSelectionChange"
                  >
                    <SelectTrigger
                      class="w-full bg-white/5 border border-white/10 text-white focus:ring-phantom-blue/50 focus:border-phantom-blue/50 hover:bg-white/10"
                    >
                      <SelectValue placeholder="Select skills..." />
                    </SelectTrigger>
                    <SelectContent
                      class="bg-gray-900/95 backdrop-blur-sm border border-white/10 text-white"
                    >
                      <div
                        v-if="skills.length > 0"
                        class="py-1.5 px-3 text-sm text-white/60"
                      >
                        {{ skills.length }} skills available
                      </div>

                      <!-- No skills message -->
                      <div
                        v-if="skills.length === 0"
                        class="py-4 px-3 text-sm text-white/60 text-center"
                      >
                        No skills available. Please check the console for
                        errors.
                      </div>

                      <!-- Skills list -->
                      <template v-else>
                        <SelectItem
                          v-for="skill in skills"
                          :key="skill.id"
                          :value="skill.id"
                        >
                          {{ skill.name || "Unnamed Skill" }}
                        </SelectItem>
                      </template>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    :disabled="isLoading || selectedSkillIds.length === 0"
                    class="whitespace-nowrap border-phantom-blue/30 text-white hover:bg-phantom-blue/20"
                    @click="clearSkillSelection"
                  >
                    Clear
                  </Button>
                </div>

                <!-- Selected Skills Display -->
                <div
                  v-if="validSelectedSkillIds.length > 0"
                  class="mt-4 flex flex-wrap gap-2"
                >
                  <div
                    v-for="skillId in validSelectedSkillIds"
                    :key="skillId"
                    class="inline-flex items-center bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30 text-xs px-3 py-1.5 rounded-full"
                  >
                    {{ getSkillName(skillId) }}
                    <button
                      type="button"
                      class="ml-2 text-phantom-blue hover:text-white hover:bg-phantom-blue/30 rounded-full p-0.5 transition-colors"
                      @click="removeSkill(skillId)"
                    >
                      <SvgIcon name="x" size="xs" />
                    </button>
                  </div>
                </div>
                <div
                  v-if="
                    hasInteractedWithSkills && selectedSkillIds.length === 0
                  "
                  class="mt-2 text-xs text-red-400"
                >
                  Please select at least one skill
                </div>
              </div>
            </section>

            <!-- Question Selection Mode Section -->
            <section
              class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 question-selection-mode z-layer-low"
            >
              <h2
                class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
              >
                Question Selection Mode
              </h2>

              <div>
                <label class="block text-white font-medium mb-3"
                  >How should questions be selected?</label
                >
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-colors cursor-pointer group"
                    @click="questionSelectionMode = 'dynamic'"
                  >
                    <div class="flex items-center">
                      <div class="relative flex items-center justify-center">
                        <input
                          id="dynamicMode"
                          v-model="questionSelectionMode"
                          type="radio"
                          name="questionSelectionMode"
                          value="dynamic"
                          class="h-5 w-5 text-phantom-blue focus:ring-phantom-blue/50 border-white/20 bg-white/5"
                        />
                        <div
                          v-if="questionSelectionMode === 'dynamic'"
                          class="absolute inset-0 bg-phantom-blue/20 rounded-full animate-ping-slow"
                        />
                      </div>
                      <label
                        for="dynamicMode"
                        class="ml-3 text-base text-white font-medium group-hover:text-phantom-blue transition-colors"
                      >
                        Dynamic Mode
                      </label>
                    </div>
                    <div class="mt-3 ml-8 text-sm text-white/80">
                      Questions are randomly selected from the skill's question
                      pool for each session.
                    </div>
                  </div>

                  <div
                    class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-colors cursor-pointer group"
                    @click="questionSelectionMode = 'fixed'"
                  >
                    <div class="flex items-center">
                      <div class="relative flex items-center justify-center">
                        <input
                          id="fixedMode"
                          v-model="questionSelectionMode"
                          type="radio"
                          name="questionSelectionMode"
                          value="fixed"
                          class="h-5 w-5 text-phantom-blue focus:ring-phantom-blue/50 border-white/20 bg-white/5"
                        />
                        <div
                          v-if="questionSelectionMode === 'fixed'"
                          class="absolute inset-0 bg-phantom-blue/20 rounded-full animate-ping-slow"
                        />
                      </div>
                      <label
                        for="fixedMode"
                        class="ml-3 text-base text-white font-medium group-hover:text-phantom-blue transition-colors"
                      >
                        Fixed Mode
                      </label>
                    </div>
                    <div class="mt-3 ml-8 text-sm text-white/80">
                      Same questions for all sessions. You'll select specific
                      questions below.
                    </div>
                  </div>
                </div>

                <div
                  class="mt-6 text-sm text-white/90 bg-phantom-indigo/10 backdrop-blur-sm border border-phantom-indigo/20 rounded-xl p-5"
                >
                  <p class="font-medium text-phantom-indigo">Important Note:</p>
                  <ul class="list-disc list-inside mt-2 space-y-1 ml-2">
                    <li>
                      <strong>Dynamic mode:</strong> Questions are randomly
                      selected from the skill's question pool for each session.
                    </li>
                    <li>
                      <strong>Fixed mode:</strong> After creating the
                      assessment, you'll need to go to "Add Fixed Questions" to
                      select specific questions that will be used for all
                      sessions.
                    </li>
                  </ul>
                </div>
              </div>
            </section>
          </div>
        </div>
        <!-- Status Section -->
        <section class="w-full">
          <!-- Loading indicator -->
          <div
            v-if="isLoading"
            class="flex justify-center items-center py-8 bg-white/5 backdrop-blur-sm rounded-xl"
          >
            <div
              class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"
            />
            <span class="ml-4 text-white text-lg">Creating assessment...</span>
          </div>

          <!-- Error/Success message - Only show error messages here -->
          <div v-if="message && !isSuccess" class="my-4 message-container">
            <div
              class="bg-red-500/10 border-red-500/30 px-6 py-4 rounded-xl border backdrop-blur-sm text-white"
            >
              {{ message }}
            </div>
          </div>

          <!-- Hidden div to maintain createdAssessmentDetails state -->
          <div v-if="createdAssessmentDetails" class="hidden"></div>
        </section>

        <!-- Add Fixed Questions Section (shown when fixed mode is selected and assessment not yet created) -->
        <section
          v-if="questionSelectionMode === 'fixed' && !createdAssessmentDetails"
          class="card-phantom p-6 mt-8"
        >
          <h2
            class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-indigo to-phantom-purple bg-clip-text text-transparent"
          >
            Add Fixed Questions
          </h2>

          <!-- Instructions -->
          <div
            class="mb-6 bg-phantom-indigo/10 backdrop-blur-sm border border-phantom-indigo/20 rounded-xl p-5"
          >
            <p class="text-white/90 flex items-start">
              <SvgIcon
                name="warning"
                class="mr-2 flex-shrink-0 mt-0.5 text-phantom-indigo"
              />
              <span>
                Select your questions below. When you click "Create Assessment",
                both the assessment and the selected questions will be created
                together.
              </span>
            </p>
          </div>

          <!-- Question Distribution -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-300 mb-4">
              Question Distribution
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-gray-800/50 p-4">
                <Label
                  for="easy-questions"
                  class="text-green-400 font-medium block mb-2"
                  >Easy Questions</Label
                >
                <Input
                  id="easy-questions"
                  v-model.number="manualQuestionCounts.easy"
                  name="easy-questions"
                  type="number"
                  min="10"
                  autocomplete="off"
                  placeholder="10"
                  class="text-base"
                />
                <div class="mt-1 text-xs text-gray-400">
                  Minimum: 10 questions
                </div>
              </div>

              <div class="bg-gray-800/50 p-4">
                <Label
                  for="intermediate-questions"
                  class="text-yellow-400 font-medium block mb-2"
                  >Intermediate Questions</Label
                >
                <Input
                  id="intermediate-questions"
                  v-model.number="manualQuestionCounts.intermediate"
                  name="intermediate-questions"
                  type="number"
                  min="10"
                  autocomplete="off"
                  placeholder="10"
                  class="text-base"
                />
                <div class="mt-1 text-xs text-gray-400">
                  Minimum: 10 questions
                </div>
              </div>

              <div class="bg-gray-800/50 p-4">
                <Label
                  for="advanced-questions"
                  class="text-red-400 font-medium block mb-2"
                  >Advanced Questions</Label
                >
                <Input
                  id="advanced-questions"
                  v-model.number="manualQuestionCounts.advanced"
                  name="advanced-questions"
                  type="number"
                  min="10"
                  autocomplete="off"
                  placeholder="10"
                  class="text-base"
                />
                <div class="mt-1 text-xs text-gray-400">
                  Minimum: 10 questions
                </div>
              </div>
            </div>
            <div class="mt-3 text-sm text-gray-400">
              Specify the number of questions for each difficulty level. You can
              add more than the minimum requirements.
            </div>
          </div>

          <!-- Selected Questions Summary -->
          <div class="mb-6">
            <div class="flex justify-between items-center mb-3">
              <h3 class="text-lg font-medium text-gray-300">
                Selected Questions
              </h3>
              <span
                class="text-indigo-300 bg-indigo-900/30 px-3 py-1 rounded-full text-sm"
              >
                {{ getSelectedQuestionCount() }} selected
              </span>
            </div>

            <div class="bg-gray-800/50 p-4 min-h-[80px]">
              <div
                v-if="getSelectedQuestionCount() === 0"
                class="text-gray-500 text-sm italic flex items-center justify-center h-16"
              >
                No questions selected. Select questions from the list below.
              </div>
              <div v-else class="flex flex-wrap gap-2">
                <div
                  v-for="id in getSelectedQuestionIds()"
                  :key="id"
                  class="flex items-center bg-indigo-900/40 text-indigo-200 text-sm px-3 py-1.5 rounded"
                >
                  <span>ID: {{ id }}</span>
                  <Button
                    variant="ghost"
                    size="xs"
                    class="ml-2 text-indigo-300 hover:text-white hover:bg-indigo-900/30 rounded px-1"
                    @click="removeQuestionFromSelection(id)"
                  >
                    &times;
                  </Button>
                </div>
              </div>
            </div>

            <!-- Selected Questions Summary for Fixed Mode -->
            <div class="mt-2">
              <div class="text-sm text-gray-400">
                <span v-if="getSelectedQuestionCount() > 0">
                  {{ getSelectedQuestionCount() }} questions selected
                  <span
                    v-if="getTotalManualQuestions() > 0"
                    :class="{
                      'text-green-400':
                        getSelectedQuestionCount() ===
                        getTotalManualQuestions(),
                      'text-yellow-400':
                        getSelectedQuestionCount() < getTotalManualQuestions(),
                      'text-red-400':
                        getSelectedQuestionCount() > getTotalManualQuestions(),
                    }"
                  >
                    ({{ getTotalManualQuestions() }} required)
                  </span>
                </span>
                <span v-else class="text-yellow-400">
                  Please select questions before creating the assessment
                </span>
              </div>
            </div>
          </div>

          <!-- No Questions Available Message -->
          <div
            v-if="
              selectedSkillIds.length > 0 &&
              availableQuestions.length === 0 &&
              !isLoadingQuestions
            "
            class="mb-6 bg-yellow-900/20 p-4"
          >
            <p class="text-yellow-300 flex items-start">
              <SvgIcon
                name="warning"
                class="mr-2 flex-shrink-0 mt-0.5 text-yellow-300"
              />
              <span>
                No questions available for the selected skills. Please select
                different skills or add questions to these skills first.
              </span>
            </p>
          </div>

          <!-- Loading Questions -->
          <div
            v-if="isLoadingQuestions"
            class="mb-6 flex justify-center items-center py-6 bg-gray-800/30"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"
            />
            <span class="ml-4 text-gray-300 text-base"
              >Loading questions...</span
            >
          </div>

          <!-- Available Questions -->
          <div v-if="availableQuestions.length > 0" class="mb-6">
            <div
              class="flex flex-col md:flex-row md:justify-between md:items-center mb-4 space-y-3 md:space-y-0"
            >
              <h3 class="text-lg font-medium text-gray-300">
                Available Questions
              </h3>

              <!-- Random Selection Button -->
              <Button
                variant="generalAction"
                size="skillButton"
                :disabled="availableQuestions.length === 0"
                title="Randomly select questions based on assessment requirements"
                class="md:ml-auto"
                @click.prevent="selectRandomQuestions"
              >
                <span class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  Random Select
                </span>
              </Button>
            </div>

            <!-- Search and Filter -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div class="md:col-span-3">
                <Input
                  id="search-questions"
                  v-model="searchQuery"
                  name="search-questions"
                  placeholder="Search questions..."
                  class="w-full"
                />
              </div>
              <div>
                <select
                  id="difficulty-filter"
                  v-model="difficultyFilter"
                  name="difficulty-filter"
                  class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 [&>option]:bg-gray-800 [&>option]:text-white"
                >
                  <option value="all">All Difficulties</option>
                  <option value="easy">Easy</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
            </div>

            <div class="bg-gray-800/30 p-4 max-h-[500px] overflow-y-auto">
              <div
                v-if="filteredQuestions.length === 0"
                class="text-center py-8 text-gray-400"
              >
                No questions match your filters.
              </div>
              <div v-else class="space-y-4">
                <div
                  v-for="question in filteredQuestions"
                  :key="question.que_id"
                  class="p-4 bg-gray-800/50 hover:bg-gray-800/70 transition-all"
                  :class="{
                    'bg-indigo-900/20 border-l-4 border-l-indigo-500':
                      isQuestionSelected(question.que_id),
                  }"
                >
                  <div
                    class="flex flex-col md:flex-row md:justify-between md:items-start gap-3"
                  >
                    <div class="flex-1">
                      <div class="flex flex-wrap items-center gap-2 mb-2">
                        <span
                          class="px-2 py-1 text-xs rounded-full"
                          :class="{
                            'bg-green-900/50 text-green-400':
                              question.level === 'easy',
                            'bg-yellow-900/50 text-yellow-400':
                              question.level === 'intermediate',
                            'bg-red-900/50 text-red-400':
                              question.level === 'advanced',
                          }"
                        >
                          {{
                            question.level.charAt(0).toUpperCase() +
                            question.level.slice(1)
                          }}
                        </span>
                        <span class="text-gray-400 text-sm"
                          >ID: {{ question.que_id }}</span
                        >
                        <span class="text-gray-400 text-sm">{{
                          question.skill_name
                        }}</span>
                      </div>
                      <div class="text-white">
                        {{ question.question }}
                      </div>
                    </div>
                    <div class="md:ml-4 flex-shrink-0">
                      <Button
                        variant="generalAction"
                        size="skillButton"
                        :class="
                          isQuestionSelected(question.que_id)
                            ? 'bg-indigo-700/70 text-indigo-200 hover:bg-indigo-600/70'
                            : 'bg-indigo-900/50 text-indigo-400 hover:bg-indigo-800/50'
                        "
                        @click="addQuestionToSelection(question.que_id)"
                      >
                        {{
                          isQuestionSelected(question.que_id)
                            ? "Selected"
                            : "Select"
                        }}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Submit button (hidden after assessment is created) -->
        <section v-if="!createdAssessmentDetails" class="w-full mt-8 mb-4">
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="
                isLoading || isLoadingQuestions || selectedSkillIds.length === 0
              "
              class="btn-phantom px-6 py-3"
              :class="{
                'opacity-50 cursor-not-allowed': selectedSkillIds.length === 0,
              }"
            >
              <span class="flex items-center">
                <SvgIcon
                  v-if="isLoading"
                  name="spinner"
                  class="-ml-1 mr-2 text-white animate-spin"
                />
                <SvgIcon v-else name="plus" class="mr-2" />
                {{ isLoading ? "Creating..." : "Create Assessment" }}
              </span>
            </button>
          </div>
        </section>
      </form>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { debug, info, warning } from "@/utils/logger";
import { useNavigation } from "@/composables";
import PhantomLayout from "@/components/layout/Layout.vue";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SvgIcon from "@/components/SvgIcon.vue";

// Composables
const navigation = useNavigation();

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } =
  useMessageHandler();

// Form data
const assessmentName = ref("");
const assessmentDescription = ref("");
const selectedSkillIds = ref([]);
const questionSelectionMode = ref("dynamic");
const assessmentDuration = ref(20);
const hasInteractedWithSkills = ref(false);
const skills = ref([]);
const isLoading = ref(false);
const createdAssessmentDetails = ref(null);

// Fixed questions data
const availableQuestions = ref([]);
const questionIds = ref("");
const isLoadingQuestions = ref(false);
const manualQuestionCounts = ref({
  easy: 10,
  intermediate: 10,
  advanced: 10,
});
const searchQuery = ref("");
const difficultyFilter = ref("all");

// Computed property for valid selected skill IDs
const validSelectedSkillIds = computed(() => {
  return selectedSkillIds.value.filter(
    (id) => id !== null && id !== undefined && id !== "",
  );
});

// Helper functions for skill management
const getSkillName = (skillId) => {
  // Defensive check to ensure skills.value is an array
  if (!Array.isArray(skills.value)) {
    warning("Skills is not an array", { skills: skills.value });
    return `Skill ${skillId}`;
  }

  // Handle null or undefined skillId
  if (skillId === null || skillId === undefined) {
    warning("Null or undefined skillId passed to getSkillName");
    return "Unknown Skill";
  }

  // First, try to find by hash ID (frontend display ID)
  let skill = skills.value.find((s) => s.id === skillId);

  // If not found by hash ID, try to find by numeric ID
  if (!skill) {
    // If skillId is numeric, try to find by numericId field
    if (typeof skillId === "number" || /^\d+$/.test(skillId)) {
      const numericId =
        typeof skillId === "number" ? skillId : parseInt(skillId, 10);
      skill = skills.value.find((s) => s.numericId === numericId);
    }
  }

  // Return the skill name if found
  if (skill && skill.name) {
    return skill.name;
  } else {
    // If no skill found, return a formatted skill ID
    warning(
      `No skill found with ID: ${skillId} (checked both hash and numeric)`,
    );
    return `Skill ${skillId}`;
  }
};

const removeSkill = (skillId) => {
  selectedSkillIds.value = selectedSkillIds.value.filter(
    (id) => id !== skillId,
  );
  // Clean up any remaining null/undefined values
  cleanupSelectedSkillIds();
  // Fetch questions again if in fixed mode
  if (
    questionSelectionMode.value === "fixed" &&
    selectedSkillIds.value.length > 0
  ) {
    fetchQuestionsForSkills();
  } else if (selectedSkillIds.value.length === 0) {
    availableQuestions.value = [];
  }
};

// Clean up the selectedSkillIds array to remove null/undefined values
const cleanupSelectedSkillIds = () => {
  const originalLength = selectedSkillIds.value.length;
  selectedSkillIds.value = selectedSkillIds.value.filter(
    (id) => id !== null && id !== undefined && id !== "",
  );

  if (selectedSkillIds.value.length !== originalLength) {
    debug("Cleaned up selectedSkillIds, removed null/undefined values");
  }
};

// This function is called when the skill selection changes
const onSkillSelectionChange = () => {
  // First, clean up any null/undefined values
  cleanupSelectedSkillIds();

  // Validate that all selected skill IDs exist in the skills array
  if (selectedSkillIds.value.length > 0) {
    const validIds = selectedSkillIds.value.filter((id) => {
      // Check if this ID exists in the skills array
      const exists = skills.value.some((skill) => skill.id === id);
      if (!exists) {
        warning(
          `Removing invalid skill ID: ${id} - skill not found in loaded skills`,
        );
      }
      return exists;
    });

    // Update the selectedSkillIds with validated IDs only if there were invalid ones
    if (validIds.length !== selectedSkillIds.value.length) {
      debug(
        `Cleaned up skill selection: ${selectedSkillIds.value.length} -> ${validIds.length} valid skills`,
      );
      selectedSkillIds.value = validIds;
    }
  }

  // The watcher will automatically handle fetching questions
  hasInteractedWithSkills.value = true;
};

// Helper function to normalize skills data
const normalizeSkillsData = (filteredSkills) => {
  return filteredSkills
    .map((skill) => {
      debug("Processing skill for normalization", { skill });

      // For display purposes, use id_hash (for security/obfuscation) or fallback to id
      const displayId = skill.id_hash || skill.id;

      // For API calls, we need the numeric ID
      // If skill.id is numeric, use it; if it's a hash, we'll decode it later
      let numericId = null;

      // Check if skill.id is already a numeric ID
      if (typeof skill.id === "number" && skill.id > 0) {
        numericId = skill.id;
      } else if (typeof skill.id === "string" && /^\d+$/.test(skill.id)) {
        numericId = parseInt(skill.id, 10);
      }
      // If we have a numeric_id field, use that
      else if (
        skill.numeric_id &&
        (typeof skill.numeric_id === "number" ||
          /^\d+$/.test(skill.numeric_id))
      ) {
        numericId =
          typeof skill.numeric_id === "number"
            ? skill.numeric_id
            : parseInt(skill.numeric_id, 10);
      }
      // If we don't have a numeric ID but have a hash, we'll need to decode it
      else if (
        skill.id_hash ||
        (typeof skill.id === "string" && !/^\d+$/.test(skill.id))
      ) {
        // Mark this skill as needing hash decoding
        numericId = "NEEDS_DECODING";
      }

      if (!displayId) {
        warning("Skill without display ID", { skill });
        return null;
      }

      try {
        return {
          ...skill,
          id: displayId, // Use hash ID for frontend display and selection
          numericId: numericId, // Store numeric ID for API calls (or 'NEEDS_DECODING')
          name: skill.name || "Unnamed Skill",
        };
      } catch (e) {
        warning("Error normalizing skill", { error: e, skill });
        return null;
      }
    })
    .filter((skill) => skill !== null);
};

// Helper function to handle non-array responses
const handleNonArraySkillsResponse = (skillsData) => {
  // Try to handle non-array responses more gracefully
  warning("Response is not an array", { skillsData });

  // If it's an object with items or data property, try to use that
  if (skillsData && typeof skillsData === "object") {
    const possibleArrays = ["items", "data", "skills", "results"];
    for (const prop of possibleArrays) {
      if (Array.isArray(skillsData[prop])) {
        debug(`Found skills array in response.${prop}`);
        return skillsData[prop];
      }
    }
  }

  // If we get here, we couldn't find a valid skills array
  warning("Invalid skills response format", { skillsData });
  return [];
};

// Fetch skills from API
const fetchSkills = async () => {
  try {
    isLoading.value = true;
    const response = await api.admin.getSkills({ limit: 100, offset: 0 });

    // Use standardized response data extraction
    const skillsData = extractResponseData(response);

    if (Array.isArray(skillsData)) {
      debug("Fetched skills from API", {
        skillsCount: skillsData.length,
        sampleData: skillsData.slice(0, 2),
      });

      // Basic filtering - only filter out null/undefined skills
      const filteredSkills = skillsData.filter((skill) => {
        // Basic validation - must have an object
        if (!skill) {
          warning("Filtering out null/undefined skill");
          return false;
        }

        return true;
      });

      // Normalize skills - keep both hash ID for frontend and numeric ID for API
      const normalizedSkills = normalizeSkillsData(filteredSkills);

      skills.value = normalizedSkills;
      info("Skills loaded successfully", { skillsCount: skills.value.length });

      // Clean up any invalid selected skill IDs after skills are loaded
      cleanupSelectedSkillIds();
    } else {
      const fallbackSkills = handleNonArraySkillsResponse(skillsData);
      if (fallbackSkills.length > 0) {
        skills.value = fallbackSkills;
        return;
      }

      // If we get here, we couldn't find a valid skills array
      skills.value = [];
      setErrorMessage("No skills found or invalid response format");
    }
  } catch (error) {
    const errorInfo = extractErrorInfo(error);
    error("Error in fetchSkills", { error: errorInfo });
    logError(error, "fetchSkills");
    setErrorMessage(errorInfo.message || "Failed to fetch skills");
    skills.value = []; // Ensure skills is always an array
  } finally {
    isLoading.value = false;
  }
};

// Validation functions

// Create assessment function
const createAssessment = async () => {
  try {
    // Clear any previous messages
    clearMessage();

    // Validate form data
    if (!assessmentName.value.trim()) {
      setErrorMessage("Assessment name is required");
      return;
    }

    if (!assessmentDescription.value.trim()) {
      setErrorMessage("Assessment description is required");
      return;
    }

    if (assessmentDescription.value.trim().length < 20) {
      setErrorMessage("Assessment description must be at least 20 characters long");
      return;
    }

    if (validSelectedSkillIds.value.length === 0) {
      setErrorMessage("Please select at least one skill");
      return;
    }

    if (!assessmentDuration.value || assessmentDuration.value < 5 || assessmentDuration.value > 180) {
      setErrorMessage("Assessment duration must be between 5 and 180 minutes");
      return;
    }

    // For fixed mode, validate question selection
    if (questionSelectionMode.value === 'fixed') {
      const selectedQuestionCount = getSelectedQuestionCount();
      const totalRequired = getTotalManualQuestions();

      if (selectedQuestionCount === 0) {
        setErrorMessage("Please select questions for fixed mode assessment");
        return;
      }

      if (selectedQuestionCount !== totalRequired) {
        setErrorMessage(
          `You have selected ${selectedQuestionCount} questions but specified ${totalRequired} questions in the manual entry. Please adjust either your selection or the manual counts to match.`
        );
        return;
      }
    }

    // Get user info from localStorage
    const userInfoStr = localStorage.getItem("user_info");
    if (!userInfoStr) {
      setErrorMessage("User authentication required. Please log in again.");
      return;
    }

    let userInfo;
    try {
      userInfo = JSON.parse(userInfoStr);
    } catch (e) {
      setErrorMessage("Invalid user session. Please log in again.");
      return;
    }

    if (!userInfo.sub) {
      setErrorMessage("User ID not found. Please log in again.");
      return;
    }

    // Convert skill IDs to numeric values for API
    const numericSkillIds = validSelectedSkillIds.value.map(skillId => {
      const skill = skills.value.find(s => s.id === skillId);
      if (skill && skill.numericId && skill.numericId !== 'NEEDS_DECODING') {
        return skill.numericId;
      }
      // Fallback: try to parse as number
      const parsed = parseInt(skillId, 10);
      if (!isNaN(parsed)) {
        return parsed;
      }
      throw new Error(`Invalid skill ID: ${skillId}`);
    });

    // Prepare API request data
    const requestData = {
      topic: assessmentDescription.value.trim(),
      quiz_name: assessmentName.value.trim(),
      user_id: userInfo.sub,
      skill_ids: numericSkillIds,
      question_selection_mode: questionSelectionMode.value,
      duration: parseInt(assessmentDuration.value, 10)
    };

    debug("Creating assessment with data:", requestData);

    // Set loading state
    isLoading.value = true;

    // Call API to create assessment
    const response = await api.admin.createAssessment(requestData);
    const responseData = extractResponseData(response);

    if (responseData) {
      // Store created assessment details
      createdAssessmentDetails.value = responseData;

      info("Assessment created successfully:", responseData);

      // Handle fixed mode questions if any are selected
      if (questionSelectionMode.value === 'fixed' && getSelectedQuestionCount() > 0) {
        try {
          await addFixedQuestions();
          setSuccessMessage("Assessment created successfully with selected questions!");
        } catch (error) {
          warning("Assessment created but failed to add questions:", error);
          setSuccessMessage("Assessment created successfully! Please add questions manually from the assessment list.");
        }
      } else {
        setSuccessMessage("Assessment created successfully!");
      }

      // Navigate back to assessments list after a short delay
      setTimeout(() => {
        navigation.goToAssessmentsList();
      }, 2000);

    } else {
      setErrorMessage("Failed to create assessment. Please try again.");
    }

  } catch (error) {
    const errorInfo = extractErrorInfo(error);
    logError(error, "createAssessment");
    setErrorMessage(errorInfo.message || "An unexpected error occurred while creating the assessment");
  } finally {
    isLoading.value = false;
  }
};

// Helper function to set error message and scroll to top
const setErrorMessageWithScroll = (message) => {
  setErrorMessage(message);
  // Scroll to top to show error message
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// Fixed Questions Functions

// Fetch questions for selected skills (used when in fixed mode)
const fetchQuestionsForSkills = async () => {
  if (selectedSkillIds.value.length === 0) {
    availableQuestions.value = [];
    return;
  }

  isLoadingQuestions.value = true;
  try {
    // Fetch questions for all selected skills
    const allQuestions = [];
    for (const skillId of selectedSkillIds.value) {
      try {
        const response = await api.admin.getSkillQuestions(skillId);
        const responseData = extractResponseData(response);

        if (responseData && responseData.questions) {
          // Add skill_name to each question for display purposes
          const questionsWithSkillName = responseData.questions.map(
            (question) => ({
              ...question,
              skill_name: responseData.skill_name,
            }),
          );
          allQuestions.push(...questionsWithSkillName);
        }
      } catch (error) {
        warning(`Failed to fetch questions for skill ${skillId}:`, error);
      }
    }

    // Remove duplicates based on question ID
    const uniqueQuestions = allQuestions.filter(
      (question, index, self) =>
        index === self.findIndex((q) => q.que_id === question.que_id),
    );

    availableQuestions.value = uniqueQuestions;
  } catch (error) {
    logError(error, "fetchQuestionsForSkills");
    setErrorMessage(
      getErrorMessage(error, "Failed to fetch questions for selected skills"),
    );
  } finally {
    isLoadingQuestions.value = false;
  }
};

// Computed property for filtered questions
const filteredQuestions = computed(() => {
  let filtered = availableQuestions.value;

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(
      (q) =>
        q.question.toLowerCase().includes(query) ||
        q.skill_name.toLowerCase().includes(query),
    );
  }

  // Filter by difficulty
  if (difficultyFilter.value !== "all") {
    filtered = filtered.filter((q) => q.level === difficultyFilter.value);
  }

  return filtered;
});

// Helper methods for question selection
const getSelectedQuestionIds = () => {
  return questionIds.value
    .split(",")
    .map((id) => id.trim())
    .filter((id) => id)
    .map((id) => parseInt(id));
};

const getSelectedQuestionCount = () => {
  return getSelectedQuestionIds().length;
};

const getTotalManualQuestions = () => {
  return (
    manualQuestionCounts.value.easy +
    manualQuestionCounts.value.intermediate +
    manualQuestionCounts.value.advanced
  );
};

const isQuestionSelected = (questionId) => {
  return getSelectedQuestionIds().includes(questionId);
};

const addQuestionToSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();

  if (selectedIds.includes(questionId)) {
    // Remove the question if already selected
    removeQuestionFromSelection(questionId);
  } else {
    // Add the question if not already selected
    selectedIds.push(questionId);
    questionIds.value = selectedIds.join(", ");
  }
};

const removeQuestionFromSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();
  const updatedIds = selectedIds.filter((id) => id !== questionId);
  questionIds.value = updatedIds.join(", ");
};

// Helper functions for random question selection
const validateQuestionCountMinimums = () => {
  if (manualQuestionCounts.value.easy < 10) {
    setErrorMessageWithScroll(
      "Easy questions must be at least 10. You can specify more than 10 if needed.",
    );
    return false;
  }
  if (manualQuestionCounts.value.intermediate < 10) {
    setErrorMessageWithScroll(
      "Intermediate questions must be at least 10. You can specify more than 10 if needed.",
    );
    return false;
  }
  if (manualQuestionCounts.value.advanced < 10) {
    setErrorMessageWithScroll(
      "Advanced questions must be at least 10. You can specify more than 10 if needed.",
    );
    return false;
  }
  return true;
};

const groupQuestionsByDifficulty = () => {
  return {
    easy: availableQuestions.value.filter((q) => q.level === "easy"),
    intermediate: availableQuestions.value.filter((q) => q.level === "intermediate"),
    advanced: availableQuestions.value.filter((q) => q.level === "advanced"),
  };
};

const validateQuestionAvailability = (questionGroups) => {
  const { easy: easyQuestions, intermediate: intermediateQuestions, advanced: advancedQuestions } = questionGroups;
  const easyCount = manualQuestionCounts.value.easy;
  const intermediateCount = manualQuestionCounts.value.intermediate;
  const advancedCount = manualQuestionCounts.value.advanced;

  if (easyQuestions.length < easyCount) {
    setErrorMessageWithScroll(
      `Not enough easy questions available. Required: ${easyCount}, Available: ${easyQuestions.length}`,
    );
    return false;
  }
  if (intermediateQuestions.length < intermediateCount) {
    setErrorMessageWithScroll(
      `Not enough intermediate questions available. Required: ${intermediateCount}, Available: ${intermediateQuestions.length}`,
    );
    return false;
  }
  if (advancedQuestions.length < advancedCount) {
    setErrorMessageWithScroll(
      `Not enough advanced questions available. Required: ${advancedCount}, Available: ${advancedQuestions.length}`,
    );
    return false;
  }
  return true;
};

const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

const selectQuestionsFromGroups = (questionGroups) => {
  const { easy: easyQuestions, intermediate: intermediateQuestions, advanced: advancedQuestions } = questionGroups;
  const easyCount = manualQuestionCounts.value.easy;
  const intermediateCount = manualQuestionCounts.value.intermediate;
  const advancedCount = manualQuestionCounts.value.advanced;

  // Shuffle and select questions by difficulty
  const selectedEasy = shuffleArray([...easyQuestions]).slice(0, easyCount);
  const selectedIntermediate = shuffleArray([...intermediateQuestions]).slice(0, intermediateCount);
  const selectedAdvanced = shuffleArray([...advancedQuestions]).slice(0, advancedCount);

  // Combine all selected questions
  return [...selectedEasy, ...selectedIntermediate, ...selectedAdvanced];
};

// Randomly select questions based on assessment requirements
const selectRandomQuestions = () => {
  if (!validateQuestionCountMinimums()) {
    return;
  }

  const questionGroups = groupQuestionsByDifficulty();

  if (!validateQuestionAvailability(questionGroups)) {
    return;
  }

  const selectedQuestions = selectQuestionsFromGroups(questionGroups);

  // Update the selection
  questionIds.value = selectedQuestions.map((q) => q.que_id).join(", ");

  // Show success message
  setSuccessMessage(
    `Randomly selected ${selectedQuestions.length} questions based on assessment requirements.`,
  );
};

// Add fixed questions via API
const addFixedQuestions = async () => {
  if (!createdAssessmentDetails.value?.assessment_id) {
    setErrorMessageWithScroll(
      "Please create the assessment first before adding fixed questions",
    );
    return;
  }

  const selectedIds = getSelectedQuestionIds();
  if (selectedIds.length === 0) {
    setErrorMessageWithScroll("Please select at least one question");
    return;
  }

  // Validate minimum requirements
  if (manualQuestionCounts.value.easy < 10) {
    setErrorMessageWithScroll(
      "Easy questions must be at least 10. You can add more than 10 if needed.",
    );
    return;
  }
  if (manualQuestionCounts.value.intermediate < 10) {
    setErrorMessageWithScroll(
      "Intermediate questions must be at least 10. You can add more than 10 if needed.",
    );
    return;
  }
  if (manualQuestionCounts.value.advanced < 10) {
    setErrorMessageWithScroll(
      "Advanced questions must be at least 10. You can add more than 10 if needed.",
    );
    return;
  }

  // Check if selected questions match the manual counts
  const totalSelected = selectedIds.length;
  const totalRequired = getTotalManualQuestions();

  if (totalSelected !== totalRequired) {
    setErrorMessage(
      `You have selected ${totalSelected} questions but specified ${totalRequired} questions in the manual entry. Please adjust either your selection or the manual counts to match.`,
    );
    return;
  }

  isLoadingQuestions.value = true;
  clearMessage();

  try {
    // Call the API to add fixed questions
    await api.admin.addFinalQuestions({
      assessment_id: parseInt(createdAssessmentDetails.value.assessment_id),
      question_ids: selectedIds,
      quiz_name:
        createdAssessmentDetails.value.assessment_base_name || "Assessment",
      question_distribution: {
        easy: manualQuestionCounts.value.easy,
        intermediate: manualQuestionCounts.value.intermediate,
        advanced: manualQuestionCounts.value.advanced,
        total: getTotalManualQuestions(),
      },
    });

    // Don't show success message here - let the calling function handle it

    // Clear the selection after success
    questionIds.value = "";
  } catch (error) {
    logError(error, "addFixedQuestions");
    setErrorMessageWithScroll(
      getErrorMessage(
        error,
        "An unexpected error occurred while assigning questions",
      ),
    );
  } finally {
    isLoadingQuestions.value = false;
  }
};

// Watchers to automatically fetch questions when skills or mode changes
watch(
  [selectedSkillIds, questionSelectionMode],
  () => {
    if (
      questionSelectionMode.value === "fixed" &&
      selectedSkillIds.value.length > 0
    ) {
      fetchQuestionsForSkills();
    } else {
      availableQuestions.value = [];
      questionIds.value = "";
    }
  },
  { deep: true },
);

// Clear all selected skills
const clearSkillSelection = () => {
  selectedSkillIds.value = [];
  cleanupSelectedSkillIds(); // Clean up any remaining values
  hasInteractedWithSkills.value = true;
  onSkillSelectionChange();
};

onMounted(() => {
  fetchSkills();
});
</script>
