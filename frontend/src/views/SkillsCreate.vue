<template>
  <PhantomLayout title="Create Skill" :no-scroll="true">
    <!-- Full-width form section -->
    <div class="w-full max-w-4xl mx-auto px-6 pt-2 pb-6">
      <section>
        <!-- Back to Skill List Button - Top Right -->
        <div class="flex justify-end mb-3">
          <button
            class="btn-phantom-secondary px-4 py-2 text-sm"
            @click="navigateTo('/list-skills')"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Skill List
            </span>
          </button>
        </div>

        <form class="space-y-6" @submit.prevent="createSkill">
          <!-- Skill Name -->
          <div>
            <label for="skillName" class="block text-white font-medium mb-2"
              >Skill Name</label
            >
            <input
              id="skillName"
              v-model="skillName"
              name="skillName"
              type="text"
              autocomplete="off"
              placeholder="Enter skill name"
              class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
              required
            />
          </div>

          <!-- Skill Description -->
          <div>
            <div class="mb-2">
              <label
                for="skillDescription"
                class="block text-white font-medium mb-2"
              >
                Skill Description
                <span class="text-white/60"
                  >(required, will be used as topic for question
                  generation)</span
                >
              </label>
            </div>
            <textarea
              id="skillDescription"
              v-model="skillDescription"
              name="skillDescription"
              autocomplete="off"
              class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-3 text-white custom-scrollbar placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 h-32"
              placeholder="Enter a detailed description of the skill"
              required
            />
            <div class="flex justify-end mt-2">
              <button
                type="button"
                :disabled="!skillName || isGeneratingDescription"
                class="btn-phantom-secondary text-xs px-3 py-1.5"
                @click="suggestDescription"
              >
                <span v-if="isGeneratingDescription" class="flex items-center">
                  <svg
                    class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    />
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Generating...
                </span>
                <span v-else class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                  Suggest with AI
                </span>
              </button>
            </div>
            <p
              v-if="isGeneratingDescription"
              class="text-phantom-blue text-xs mt-1"
            >
              Generating description using AI...
            </p>

            <!-- AI Suggestion Section -->
            <div
              v-if="aiSuggestion && !isGeneratingDescription"
              class="mt-6 border-l-4 border-phantom-indigo/50 pl-4"
            >
              <div class="flex justify-between items-center mb-3">
                <div
                  class="text-phantom-indigo text-sm font-medium flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    />
                  </svg>
                  AI Suggestion
                </div>
                <div class="flex space-x-2">
                  <button
                    type="button"
                    class="bg-phantom-indigo/10 hover:bg-phantom-indigo/20 text-white text-xs px-3 py-1.5 rounded-lg transition-colors flex items-center"
                    @click="useAiSuggestion"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    Use
                  </button>
                  <button
                    type="button"
                    class="bg-phantom-indigo/10 hover:bg-phantom-indigo/20 text-white text-xs px-3 py-1.5 rounded-lg transition-colors flex items-center"
                    @click="copyAiSuggestion"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                    Copy
                  </button>
                </div>
              </div>
              <div
                class="py-2 px-3 text-white/90 text-sm bg-phantom-indigo/5 rounded"
              >
                {{ aiSuggestion }}

                <!-- Display the suggested level with appropriate color -->
                <div
                  v-if="suggestedLevel"
                  class="mt-3 pt-3 border-t border-white/10"
                >
                  <span class="text-white/60">Suggested Level: </span>
                  <span
                    :class="{
                      'text-green-400': suggestedLevelColor === 'green',
                      'text-yellow-400': suggestedLevelColor === 'yellow',
                      'text-orange-400': suggestedLevelColor === 'orange',
                    }"
                    >{{ suggestedLevel }}</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Loading indicator -->
          <div v-if="isLoading" class="flex justify-center items-center py-4">
            <div
              class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-phantom-blue"
            />
            <span class="ml-3 text-white">Creating skill...</span>
          </div>

          <!-- Error message -->
          <div v-if="errorMessage" class="my-4">
            <div class="border-l-4 border-red-500 pl-4 py-3 text-red-300">
              {{ errorMessage }}
            </div>
          </div>

          <!-- Success message - hidden since we're using the notification -->
          <div v-if="successMessage && false" class="my-4">
            <div class="border-l-4 border-green-500 pl-4 py-3 text-green-300">
              {{ successMessage }}
            </div>
          </div>

          <!-- Hidden div to maintain createdSkill state -->
          <div v-if="createdSkill" class="hidden"></div>

          <!-- Submit button (hidden after skill is created) -->
          <div v-if="!createdSkill" class="flex justify-end">
            <button
              type="submit"
              :disabled="isLoading"
              class="btn-phantom px-6 py-3"
            >
              <span class="flex items-center">
                <svg
                  v-if="isLoading"
                  class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                {{ isLoading ? "Creating..." : "Create Skill" }}
              </span>
            </button>
          </div>
        </form>
      </section>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { safeCopyToClipboard } from "@/utils/domHelpers";
import { extractResponseData } from "@/utils/apiResponseHandler";
import { info, error } from "@/utils/logger";
import PhantomLayout from "@/components/layout/Layout.vue";
import globalNotification from "@/composables/useNotification";

//----------------------------------------------------------------
// Dependencies & Navigation
//----------------------------------------------------------------
const router = useRouter();
const navigateTo = (path) => router.push(path);
const { showSuccess } = globalNotification;

//----------------------------------------------------------------
// Component State
//----------------------------------------------------------------

// Core form data bound to user input.
const skillName = ref("");
const skillDescription = ref("");

// State for the AI description suggestion feature.
// This is kept separate from the main `skillDescription` to give the user control.
const aiSuggestion = ref("");
const suggestedLevel = ref("");
const suggestedLevelColor = ref("");
const descriptionSuggested = ref(false);

// UI state flags for loading indicators and feedback messages.
const isLoading = ref(false); // For the final 'Create Skill' submission.
const isGeneratingDescription = ref(false); // For the AI suggestion generation.
const errorMessage = ref("");
const successMessage = ref("");
const createdSkill = ref(null);

//----------------------------------------------------------------
// AI-Powered Features
//----------------------------------------------------------------

/**
 * Calls the API to generate an AI-powered skill description based on the skill name.
 * It provides a smart UX by auto-populating the description only if the user hasn't
 * already written a significant amount.
 */
const suggestDescription = async () => {
  if (!skillName.value) {
    errorMessage.value = "Please enter a skill name first.";
    return;
  }

  // Reset state before making a new API call to ensure a clean UI.
  isGeneratingDescription.value = true;
  descriptionSuggested.value = false;
  errorMessage.value = "";
  aiSuggestion.value = "";

  try {
    const existingDescription = skillDescription.value.trim();
    const response = await api.admin.suggestSkillDescription({
      skill_name: skillName.value,
      // Provide context to the AI, allowing it to refine or build upon the user's initial thoughts.
      existing_description: existingDescription || undefined,
    });

    const responseData = extractResponseData(response);
    if (responseData?.description) {
      aiSuggestion.value = responseData.description;
      suggestedLevel.value = responseData.level || "intermediate";
      suggestedLevelColor.value = responseData.level_color || "yellow";
      descriptionSuggested.value = true;

      // For a better user experience, if the user hasn't typed much, we automatically
      // populate the main description field. Otherwise, we let them choose to use it.
      if (!existingDescription || existingDescription.length <= 10) {
        skillDescription.value = responseData.description;
      }
    } else {
      errorMessage.value =
        "Failed to generate a description. Please try again.";
    }
  } catch (err) {
    logError(err, "suggestDescription");
    errorMessage.value = getErrorMessage(
      err,
      "Failed to generate description.",
    );
  } finally {
    isGeneratingDescription.value = false;
  }
};

//----------------------------------------------------------------
// Form Actions & UI Helpers
//----------------------------------------------------------------

/**
 * Copies the AI-generated suggestion to the user's clipboard.
 */
const copyAiSuggestion = async () => {
  const success = await safeCopyToClipboard(aiSuggestion.value);
  if (success) {
    info("AI suggestion copied to clipboard");
  } else {
    error("Failed to copy text to clipboard");
  }
};

/**
 * Allows the user to explicitly use the AI suggestion, overwriting the
 * current content in the main description field.
 */
const useAiSuggestion = () => {
  if (aiSuggestion.value) {
    skillDescription.value = aiSuggestion.value;
  }
};

/**
 * Submits the form to create a new skill.
 */
const createSkill = async () => {
  // --- Form Validation ---
  if (!skillName.value || !skillDescription.value) {
    errorMessage.value = "Please fill in all required fields.";
    return;
  }
  // The description length is important for the quality of subsequent AI question generation.
  if (skillDescription.value.length < 20) {
    errorMessage.value = "Description must be at least 20 characters long.";
    return;
  }

  // --- API Call ---
  isLoading.value = true;
  errorMessage.value = "";
  successMessage.value = "";
  createdSkill.value = null;

  try {
    const response = await api.admin.createSkill({
      name: skillName.value,
      description: skillDescription.value,
    });

    const responseData = extractResponseData(response);
    createdSkill.value = responseData.skill;
    successMessage.value =
      responseData.message || "Skill created successfully!";

    // Show success notification with action to generate questions
    showSuccess("Skill created successfully! Go to Generate Questions", {
      duration: 7000,
      actionText: "Generate Questions",
      onAction: () => {
        // Use the correct property for the skill ID
        const skillId = createdSkill.value.id_hash || createdSkill.value.id;
        navigateTo(`/skill/${skillId}`);
      },
    });

    // Reset the form for the next entry.
    skillName.value = "";
    skillDescription.value = "";
    aiSuggestion.value = "";
    descriptionSuggested.value = false;
  } catch (err) {
    logError(err, "createSkill");
    createdSkill.value = null;

    // Handle a specific, common error to provide a more helpful message.
    if (err.response?.data?.detail?.includes("already exists")) {
      errorMessage.value = `A skill with the name "${skillName.value}" already exists.`;
    } else {
      errorMessage.value = getErrorMessage(
        err,
        "An error occurred while creating the skill.",
      );
    }
  } finally {
    isLoading.value = false;
  }
};
</script>
