/**
 * Composable for managing assessment-wise reports
 */
import { ref, computed } from "vue";
import { api } from "@/services/api";
import { useMessageHandler } from "@/utils/messageHandler";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { debug, warning, error } from "@/utils/logger";
import config from "../config/globalConfig";

export function useAssessmentReport() {
  const {
    message,
    isSuccess,
    setSuccessMessage,
    setErrorMessage,
    clearMessage,
  } = useMessageHandler();

  // State variables
  const assessments = ref([]);
  const selectedAssessmentId = ref("");
  const selectedAssessment = ref(null);
  const assessmentDetails = ref(null);
  const selectedLevel = ref("");
  const assessmentWiseLoading = ref(false);
  const questionStatsLoading = ref(false);
  const assessmentWiseReportGenerated = ref(false);
  const assessmentWiseReports = ref({
    baseReportUrl: "",
    scoreReportUrl: "",
  });
  const reportQuestions = ref([]);

  // Computed property to sort questions by difficulty level and filter by selected level
  const sortedQuestions = computed(() => {
    if (!selectedAssessment.value) {
      return [];
    }

    const levelOrder = { easy: 1, intermediate: 2, advanced: 3 };
    const isFixedAssessment =
      selectedAssessment.value.question_selection_mode === "fixed";

    // For fixed assessments: show all questions with statistics merged
    if (isFixedAssessment) {
      const hasAvailableQuestions =
        assessmentDetails.value?.available_questions &&
        assessmentDetails.value.available_questions.length > 0;
      const hasReportQuestions =
        reportQuestions.value && reportQuestions.value.length > 0;

      if (hasAvailableQuestions) {
        const allQuestions = [...assessmentDetails.value.available_questions];

        // Merge statistics from reportQuestions if available
        if (hasReportQuestions) {
          allQuestions.forEach((question) => {
            const reportQuestion = reportQuestions.value.find(
              (rq) => rq.que_id === question.que_id,
            );
            if (reportQuestion) {
              question.total_attended_users =
                reportQuestion.total_attended_users || 0;
              question.correct_percentage =
                reportQuestion.correct_percentage || 0;
              question.correct_answers = reportQuestion.correct_answers || 0;
              question.total_attempts = reportQuestion.total_attempts || 0;
            } else {
              question.total_attended_users = 0;
              question.correct_percentage = 0;
              question.correct_answers = 0;
              question.total_attempts = 0;
            }
          });
        } else {
          allQuestions.forEach((question) => {
            question.total_attended_users = 0;
            question.correct_percentage = 0;
            question.correct_answers = 0;
            question.total_attempts = 0;
          });
        }

        const filteredQuestions =
          selectedLevel.value && selectedLevel.value !== ""
            ? allQuestions.filter((q) => q.level === selectedLevel.value)
            : allQuestions;

        return filteredQuestions.sort((a, b) => {
          const levelA = levelOrder[a.level] || 999;
          const levelB = levelOrder[b.level] || 999;
          return levelA - levelB;
        });
      } else if (hasReportQuestions) {
        const dynamicQuestions = [...reportQuestions.value];
        const filteredQuestions =
          selectedLevel.value && selectedLevel.value !== ""
            ? dynamicQuestions.filter((q) => q.level === selectedLevel.value)
            : dynamicQuestions;

        return filteredQuestions.sort((a, b) => {
          const levelA = levelOrder[a.level] || 999;
          const levelB = levelOrder[b.level] || 999;
          return levelA - levelB;
        });
      }

      return [];
    }

    // For dynamic assessments: show only attended questions (from report data)
    if (reportQuestions.value && reportQuestions.value.length > 0) {
      const dynamicQuestions = [...reportQuestions.value];
      const filteredQuestions =
        selectedLevel.value && selectedLevel.value !== ""
          ? dynamicQuestions.filter((q) => q.level === selectedLevel.value)
          : dynamicQuestions;

      return filteredQuestions.sort((a, b) => {
        const levelA = levelOrder[a.level] || 999;
        const levelB = levelOrder[b.level] || 999;
        return levelA - levelB;
      });
    }

    return [];
  });

  // Fetch all assessments
  const fetchAssessments = async () => {
    try {
      clearMessage();
      debug("Fetching assessments...");
      const response = await api.admin.getAssessments({
        limit: 100,
        offset: 0,
      });
      debug("Raw API response:", { response });

      const assessmentData = extractResponseData(response);
      debug("Extracted assessment data:", { assessmentData });

      const assessmentList =
        assessmentData?.assessments || assessmentData || [];
      debug("Assessment list before filtering:", { assessmentList });

      if (Array.isArray(assessmentList)) {
        assessments.value = assessmentList.filter(
          (assessment) =>
            assessment &&
            typeof assessment === "object" &&
            ((assessment.id !== undefined && assessment.id !== null) ||
              (assessment.id_hash !== undefined &&
                assessment.id_hash !== null)),
        );
        debug("Filtered assessments with id or id_hash:", {
          assessments: assessments.value,
        });
      } else {
        warning("Assessment list is not an array, setting empty array");
        assessments.value = [];
      }

      if (assessments.value.length === 0) {
        setErrorMessage(
          "No assessments found. Please create assessments first.",
        );
      }
    } catch (fetchError) {
      error("Error fetching assessments:", { error: fetchError });
      logError(fetchError, "fetchAssessments");
      const errorInfo = extractErrorInfo(fetchError);
      debug("Error info:", { errorInfo });
      setErrorMessage(errorInfo.message || "Failed to fetch assessments");
      assessments.value = [];
    }
  };

  // Handle assessment selection change
  const onAssessmentChange = async (assessmentId) => {
    if (!assessmentId) {
      selectedAssessment.value = null;
      assessmentDetails.value = null;
      reportQuestions.value = [];
      selectedLevel.value = "";
      assessmentWiseReportGenerated.value = false;
      assessmentWiseReports.value = { baseReportUrl: "", scoreReportUrl: "" };
      return;
    }

    try {
      clearMessage();

      reportQuestions.value = [];
      selectedLevel.value = "";
      assessmentWiseReportGenerated.value = false;
      assessmentWiseReports.value = { baseReportUrl: "", scoreReportUrl: "" };

      selectedAssessment.value = assessments.value.find(
        (a) =>
          a &&
          ((a.id_hash && a.id_hash.toString() === assessmentId) ||
            (a.id && a.id.toString() === assessmentId)),
      );

      const response = await api.admin.getAssessment(assessmentId);
      assessmentDetails.value = extractResponseData(response);

      await fetchQuestionStatistics();
    } catch (fetchError) {
      error("Error fetching assessment details:", { error: fetchError });
      logError(fetchError, "onAssessmentChange");
      setErrorMessage(
        getErrorMessage(fetchError, "Failed to fetch assessment details"),
      );
      selectedAssessment.value = null;
      assessmentDetails.value = null;
      reportQuestions.value = [];
      selectedLevel.value = "";
    }
  };

  // Fetch question statistics for the selected assessment
  const fetchQuestionStatistics = async () => {
    if (!selectedAssessment.value) {
      return;
    }

    questionStatsLoading.value = true;

    try {
      let assessmentBaseName = selectedAssessment.value.name
        .replace(/\s+(Mock|Final)?\s*Assessment$/i, "")
        .trim();

      const quizType =
        selectedAssessment.value.question_selection_mode === "fixed"
          ? config.defaultQuizType.final
          : config.defaultQuizType.mock;

      const response = await api.admin.generateReport({
        report_type: "assessment_wise",
        assessment_base_name: assessmentBaseName,
        quiz_type: quizType,
      });

      const reportData = extractResponseData(response);
      if (reportData?.base_report && Array.isArray(reportData.base_report)) {
        reportQuestions.value = reportData.base_report;
      }
    } catch (fetchError) {
      error("Error fetching question statistics:", { error: fetchError });
      logError(fetchError, "fetchQuestionStatistics");
    } finally {
      questionStatsLoading.value = false;
    }
  };

  // Generate Assessment-wise Report
  const generateAssessmentWiseReport = async () => {
    if (!selectedAssessment.value) {
      setErrorMessage("Please select an assessment");
      return;
    }

    assessmentWiseLoading.value = true;
    clearMessage();
    assessmentWiseReportGenerated.value = false;
    assessmentWiseReports.value = { baseReportUrl: "", scoreReportUrl: "" };

    try {
      let assessmentBaseName = selectedAssessment.value.name
        .replace(/\s+(Mock|Final)?\s*Assessment$/i, "")
        .trim();

      const quizType =
        selectedAssessment.value.question_selection_mode === "fixed"
          ? config.defaultQuizType.final
          : config.defaultQuizType.mock;

      const response = await api.admin.generateReport({
        report_type: "assessment_wise",
        assessment_base_name: assessmentBaseName,
        quiz_type: quizType,
      });

      const responseData = extractResponseData(response);

      if (
        !responseData ||
        (!responseData.base_report && !responseData.score_report)
      ) {
        const errorMessage =
          responseData?.message || "No report data available";
        setErrorMessage(errorMessage);
        return;
      }

      assessmentWiseReportGenerated.value = true;
      setSuccessMessage("Assessment-wise report generated successfully!");

      if (responseData.base_report && Array.isArray(responseData.base_report)) {
        reportQuestions.value = responseData.base_report;
      }

      // Create blob URLs for CSV downloads
      if (responseData.base_report) {
        let csvContent;
        if (Array.isArray(responseData.base_report)) {
          const headers = [
            "Skill",
            "Level",
            "Question",
            "Total Users",
            "Correct Answers",
            "Total Attempts",
            "Correct %",
          ];
          const csvRows = [headers.join(",")];

          responseData.base_report.forEach((row) => {
            let totalUsers = 0;
            if (
              row.correct_answers &&
              row.correct_percentage &&
              row.correct_percentage > 0
            ) {
              totalUsers = Math.round(
                row.correct_answers / (row.correct_percentage / 100),
              );
            } else if (row.total_attempts) {
              totalUsers = row.total_attempts;
            } else {
              totalUsers = row.total_attended_users || 0;
            }

            const csvRow = [
              `"${row.skill_name || ""}"`,
              `"${row.level || ""}"`,
              `"${(row.question || "").replace(/"/g, '""')}"`,
              totalUsers,
              row.correct_answers || 0,
              row.total_attempts || 0,
              `${(row.correct_percentage || 0).toFixed(1)}%`,
            ];
            csvRows.push(csvRow.join(","));
          });

          csvContent = csvRows.join("\n");
        } else {
          csvContent = responseData.base_report;
        }

        const baseReportBlob = new Blob([csvContent], { type: "text/csv" });
        assessmentWiseReports.value.baseReportUrl =
          URL.createObjectURL(baseReportBlob);
      }

      if (responseData.score_report) {
        let csvContent;
        if (Array.isArray(responseData.score_report)) {
          const headers = [
            "User ID",
            "Topic",
            "Total Score",
            "Obtained Score",
            "Percentage",
            "Performance Level",
            "Easy Attempted",
            "Easy Correct",
            "Intermediate Attempted",
            "Intermediate Correct",
            "Advanced Attempted",
            "Advanced Correct",
          ];
          const csvRows = [headers.join(",")];

          responseData.score_report.forEach((row) => {
            const csvRow = [
              `"${row.user_id || ""}"`,
              `"${row.topic || ""}"`,
              row.total_score || 0,
              row.obtained_score || 0,
              `${(row.percentage || 0).toFixed(1)}%`,
              `"${row.performance_level || ""}"`,
              row.easy_attempted || 0,
              row.easy_correct || 0,
              row.intermediate_attempted || 0,
              row.intermediate_correct || 0,
              row.advanced_attempted || 0,
              row.advanced_correct || 0,
            ];
            csvRows.push(csvRow.join(","));
          });

          csvContent = csvRows.join("\n");
        } else {
          csvContent = responseData.score_report;
        }

        const scoreReportBlob = new Blob([csvContent], { type: "text/csv" });
        assessmentWiseReports.value.scoreReportUrl =
          URL.createObjectURL(scoreReportBlob);
      }
    } catch (generateError) {
      error("Error generating assessment-wise report:", {
        error: generateError,
      });
      logError(generateError, "generateAssessmentWiseReport");
      setErrorMessage(
        getErrorMessage(
          generateError,
          "Failed to generate assessment-wise report",
        ),
      );
    } finally {
      assessmentWiseLoading.value = false;
    }
  };

  // Calculate total users who attempted a question
  const calculateTotalUsers = (question) => {
    if (
      question.correct_answers &&
      question.correct_percentage &&
      question.correct_percentage > 0
    ) {
      return Math.round(
        question.correct_answers / (question.correct_percentage / 100),
      );
    }

    if (question.total_attempts) {
      return question.total_attempts;
    }

    return question.total_attended_users || 0;
  };

  // Toggle level filter on/off
  const toggleLevelFilter = (level) => {
    if (selectedLevel.value === level) {
      selectedLevel.value = "";
    } else {
      selectedLevel.value = level;
    }
  };

  return {
    // State
    message,
    isSuccess,
    assessments,
    selectedAssessmentId,
    selectedAssessment,
    assessmentDetails,
    selectedLevel,
    assessmentWiseLoading,
    questionStatsLoading,
    assessmentWiseReportGenerated,
    assessmentWiseReports,
    reportQuestions,
    sortedQuestions,

    // Methods
    fetchAssessments,
    onAssessmentChange,
    generateAssessmentWiseReport,
    calculateTotalUsers,
    toggleLevelFilter,
  };
}
