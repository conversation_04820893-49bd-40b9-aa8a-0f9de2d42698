import { ref, computed } from 'vue'
import { debug } from '@/utils/logger'

export function useQuizTimer() {
  const totalQuizTime = ref(3600) // Default 60 minutes
  const timeRemaining = ref(3600)
  const timerInterval = ref(null)
  const timeUp = ref(false)

  // Computed
  const formattedTime = computed(() => {
    const minutes = Math.floor(timeRemaining.value / 60)
    const seconds = timeRemaining.value % 60
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  })

  const timePercentage = computed(() => {
    if (totalQuizTime.value === 0) return 0
    return Math.max(0, (timeRemaining.value / totalQuizTime.value) * 100)
  })

  // Start timer
  const startTimer = (onTimeUp) => {
    if (timerInterval.value) return
    
    timerInterval.value = setInterval(() => {
      if (timeRemaining.value > 0) {
        timeRemaining.value--
      } else {
        timeUp.value = true
        stopTimer()
        if (onTimeUp) onTimeUp()
      }
    }, 1000)
    
    debug('Timer started', { totalTime: totalQuizTime.value })
  }

  // Stop timer
  const stopTimer = () => {
    if (timerInterval.value) {
      clearInterval(timerInterval.value)
      timerInterval.value = null
    }
  }

  // Set duration
  const setDuration = (minutes) => {
    const seconds = Math.max(1, Math.floor(Number(minutes) || 60)) * 60
    totalQuizTime.value = seconds
    timeRemaining.value = seconds
    debug('Timer duration set', { minutes, seconds })
  }

  // Reset timer
  const resetTimer = () => {
    stopTimer()
    timeRemaining.value = totalQuizTime.value
    timeUp.value = false
  }

  return {
    totalQuizTime,
    timeRemaining,
    timeUp,
    formattedTime,
    timePercentage,
    startTimer,
    stopTimer,
    setDuration,
    resetTimer
  }
}
