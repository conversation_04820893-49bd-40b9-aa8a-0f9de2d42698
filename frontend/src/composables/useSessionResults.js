/**
 * Composable for managing session results
 */
import { ref } from "vue";
import { api } from "@/services/api";
import { extractResponseData } from "@/utils/apiResponseHandler";
import { debug, error as logError } from "@/utils/logger";

export function useSessionResults() {
  const isLoading = ref(false);
  const error = ref("");
  const sessionResults = ref(null);

  const loadSessionResults = async (sessionId) => {
    if (!sessionId) return;

    isLoading.value = true;
    error.value = "";

    try {
      debug("Loading session results for:", sessionId);
      const response = await api.admin.getSessionResults(sessionId);
      const data = extractResponseData(response);

      if (data) {
        sessionResults.value = data;
        debug("Session results loaded:", data);
      } else {
        error.value = "No results data available";
      }
    } catch (err) {
      logError("Error loading session results:", err);
      error.value =
        err.response?.data?.message || "Failed to load session results";
    } finally {
      isLoading.value = false;
    }
  };

  const getPerformanceLevel = () => {
    if (!sessionResults.value) return "N/A";

    const { correct_answers, questions_attempted } =
      sessionResults.value.score_summary;

    if (questions_attempted === 0) return "Fail";

    const percentage = (correct_answers / questions_attempted) * 100;

    if (percentage === 0) return "Fail";
    if (percentage < 33) return "Basic";
    if (percentage < 62) return "Acceptable";
    if (percentage < 85) return "Exceed Expectation";
    return "OUTSTANDING";
  };

  const getPerformanceLevelClass = () => {
    const level = getPerformanceLevel();
    switch (level) {
      case "OUTSTANDING":
        return "bg-green-500/20 text-green-300 border border-green-500/30";
      case "Exceed Expectation":
        return "bg-blue-500/20 text-blue-300 border border-blue-500/30";
      case "Acceptable":
        return "bg-yellow-500/20 text-yellow-300 border border-yellow-500/30";
      case "Basic":
        return "bg-orange-500/20 text-orange-300 border border-orange-500/30";
      case "Fail":
        return "bg-red-500/20 text-red-300 border border-red-500/30";
      default:
        return "bg-white/10 text-white/70 border border-white/20";
    }
  };

  const getResultOptionClass = (key, question) => {
    if (key === question.userAnswer && key === question.correctAnswerKey) {
      return "bg-green-400/5 border-green-400/20 text-white";
    } else if (key === question.userAnswer) {
      return "bg-red-400/5 border-red-400/20 text-white";
    } else if (key === question.correctAnswerKey) {
      return "bg-green-400/5 border-green-400/20 text-white";
    } else {
      return "bg-white/5 border-white/10 text-white/70";
    }
  };

  const getResultOptionIconClass = (key, question) => {
    if (key === question.userAnswer && key === question.correctAnswerKey) {
      return "bg-green-400 text-white";
    } else if (key === question.userAnswer) {
      return "bg-red-400 text-white";
    } else if (key === question.correctAnswerKey) {
      return "bg-green-400 text-white";
    } else {
      return "bg-white/10 text-white/70";
    }
  };

  const resetResults = () => {
    sessionResults.value = null;
    error.value = "";
  };

  return {
    isLoading,
    error,
    sessionResults,
    loadSessionResults,
    getPerformanceLevel,
    getPerformanceLevelClass,
    getResultOptionClass,
    getResultOptionIconClass,
    resetResults,
  };
}
