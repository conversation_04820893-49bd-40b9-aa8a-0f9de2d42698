/**
 * Composable for managing user reports and assessments
 */
import { ref } from "vue";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { useLoadingState } from "./useUIState";
import { debug, info, warning, error } from "@/utils/logger";
import { hasAdminAccess } from "@/utils/authHelpers";

export function useUserReports() {
  const usersLoadingState = useLoadingState();
  const userAssessmentsLoadingState = useLoadingState();
  const { message, isSuccess, setErrorMessage, clearMessage } =
    useMessageHandler();

  // Data
  const users = ref([]);
  const selectedUser = ref(null);
  const userDetails = ref(null);
  const userAssessments = ref([]);
  const skillPerformanceData = ref([]);
  const isEmployee = ref(false);
  const currentUserId = ref(null);
  const currentUserEmail = ref(null);

  // Aliases
  const usersLoading = usersLoadingState.isLoading;
  const userAssessmentsLoading = userAssessmentsLoadingState.isLoading;

  // User role management
  const checkUserRole = () => {
    const userInfoStr = localStorage.getItem("user_info");
    if (!userInfoStr) return false;

    try {
      const userInfo = JSON.parse(userInfoStr);
      debug("User info from localStorage:", { userInfo });

      const userHasAdminAccess = hasAdminAccess(userInfo);
      const employeeGroupName =
        import.meta.env.VITE_EMPLOYEE_GROUP_NAME || "employees";

      if (userInfo && userInfo.groups) {
        isEmployee.value =
          userInfo.groups.includes(employeeGroupName) && !userHasAdminAccess;
        currentUserEmail.value = userInfo.email;

        if (userInfo.user_id) {
          currentUserId.value = userInfo.user_id;
        } else if (userInfo.id) {
          currentUserId.value = userInfo.id;
        } else if (userInfo.sub) {
          currentUserId.value = userInfo.sub;
        }

        debug("User role check:", {
          isEmployee: isEmployee.value,
          userEmail: currentUserEmail.value,
          userId: currentUserId.value,
          groups: userInfo.groups,
        });

        return isEmployee.value;
      }
    } catch (parseError) {
      error("Error parsing user info:", { error: parseError });
    }

    return false;
  };

  // Fetch all users
  const fetchAllUsers = async () => {
    usersLoading.value = true;
    clearMessage();
    users.value = [];

    try {
      const response = await api.admin.getUsers();
      const data = extractResponseData(response);

      if (data) {
        if (data.users && Array.isArray(data.users)) {
          users.value = data.users;
          if (users.value.length === 0) {
            setErrorMessage("No users found in the database.");
          }
        } else {
          setErrorMessage(
            "Invalid response format from server. Expected users array.",
          );

          if (typeof data === "object") {
            const possibleUsers = Object.values(data).find((val) =>
              Array.isArray(val),
            );
            if (possibleUsers && possibleUsers.length > 0) {
              users.value = possibleUsers;
            }
          }
        }
      } else {
        setErrorMessage("Invalid response from server");
      }
    } catch (err) {
      logError(err, "fetchAllUsers");
      const errorInfo = extractErrorInfo(err);
      setErrorMessage(errorInfo.message || "Failed to fetch users");
      users.value = [];
    } finally {
      usersLoading.value = false;
    }
  };

  // Find user ID by email
  const findUserIdByEmail = async (email) => {
    if (!email) {
      error("No email provided to find user ID");
      return null;
    }

    debug(`Finding user ID for email: ${email}`);

    try {
      const response = await api.admin.getUsers();
      const data = extractResponseData(response);

      if (data) {
        let usersList = [];

        if (data.users && Array.isArray(data.users)) {
          usersList = data.users;
        } else if (typeof data === "object") {
          const possibleUsers = Object.values(data).find((val) =>
            Array.isArray(val),
          );
          if (possibleUsers && possibleUsers.length > 0) {
            usersList = possibleUsers;
          }
        }

        debug(`Found ${usersList.length} users, searching for email: ${email}`);

        let user = usersList.find(
          (u) =>
            u.email && email && u.email.toLowerCase() === email.toLowerCase(),
        );

        if (user) {
          debug(`Found user with matching email:`, { user });
          return user.id;
        }

        // Try partial match
        user = usersList.find(
          (u) =>
            u.email &&
            email &&
            (u.email.toLowerCase().includes(email.toLowerCase()) ||
              email.toLowerCase().includes(u.email.toLowerCase())),
        );

        if (user) {
          debug(`Found user with partial email match:`, { user });
          return user.id;
        }

        // Try name/external_id match
        const emailUsername = email.split("@")[0];
        user = usersList.find(
          (u) =>
            (u.name &&
              u.name.toLowerCase().includes(emailUsername.toLowerCase())) ||
            (u.display_name &&
              u.display_name
                .toLowerCase()
                .includes(emailUsername.toLowerCase())) ||
            (u.external_id &&
              u.external_id
                .toLowerCase()
                .includes(emailUsername.toLowerCase())),
        );

        if (user) {
          debug(`Found user with name/id match:`, { user });
          return user.id;
        }

        warning(`No user found with email: ${email}`);
        return null;
      } else {
        error("Invalid response from server when fetching users");
        return null;
      }
    } catch (findError) {
      error("Error finding user by email:", { error: findError });
      return null;
    }
  };

  // Select user and fetch their data
  const selectUser = async (userId) => {
    selectedUser.value = userId;
    userDetails.value = null;
    userAssessments.value = [];
    skillPerformanceData.value = [];
    userAssessmentsLoading.value = true;
    clearMessage();

    try {
      const localUser = users.value.find((u) => u.id === userId);
      const userObj =
        localUser ||
        (isEmployee.value
          ? {
              id: userId,
              external_id: userId,
              name: "Current User",
              display_name: currentUserEmail.value
                ? `User (${currentUserEmail.value})`
                : "Current User",
              email: currentUserEmail.value || "N/A",
            }
          : null);

      if (!userObj) {
        setErrorMessage("User not found in local data");
        selectedUser.value = null;
        return;
      }

      debug("Using user object for data fetch:", { userObj });

      try {
        let response;

        if (
          isEmployee.value &&
          currentUserEmail.value &&
          (isNaN(userId) || userId.toString().includes("ldap"))
        ) {
          debug(`Using email endpoint for user: ${currentUserEmail.value}`);
          response = await api.user.getUserAssessmentsByEmail(
            currentUserEmail.value,
          );
        } else {
          debug(`Fetching assessments for user ID: ${userId}`);
          response = await api.admin.getUserAssessments(userId);
        }

        debug("User assessments response:", { response });
        const data = extractResponseData(response);

        if (data) {
          userDetails.value = {
            id: data.user_id || userObj.id,
            external_id: data.external_id || userObj.external_id || userObj.id,
            email: data.email || userObj.email || "N/A",
            display_name:
              data.display_name ||
              userObj.display_name ||
              userObj.name ||
              userObj.external_id ||
              "Unknown User",
          };

          if (data.assessments && Array.isArray(data.assessments)) {
            if (data.assessments.length === 0) {
              info("No assessments found for user");
              userAssessments.value = [
                {
                  session_id: Date.now(),
                  assessment_name: "No Assessments Found",
                  mode: new Date().toISOString(),
                  status: "no_data",
                  score: 0,
                  easy_count: 0,
                  intermediate_count: 0,
                  advanced_count: 0,
                  session_created: new Date().toISOString(),
                  session_completed: null,
                  assessment_id: 0,
                  assessment_description:
                    "You have not taken any assessments yet.",
                  max_score: 0,
                  percentage_score: 0,
                },
              ];
            } else {
              userAssessments.value = data.assessments.map((assessment) => ({
                session_id: assessment.session_id || Date.now(),
                assessment_name:
                  assessment.assessment_name || "Unknown Assessment",
                mode:
                  assessment.mode ||
                  assessment.session_created ||
                  new Date().toISOString(),
                status: assessment.status || "unknown",
                score: parseInt(assessment.score || 0),
                easy_count: parseInt(assessment.easy_count || 0),
                intermediate_count: parseInt(
                  assessment.intermediate_count || 0,
                ),
                advanced_count: parseInt(assessment.advanced_count || 0),
                session_created: assessment.session_created,
                session_completed: assessment.session_completed,
                assessment_id: assessment.assessment_id,
                assessment_description: assessment.assessment_description,
                max_score: parseInt(assessment.max_score || 0),
                percentage_score: parseInt(assessment.percentage_score || 0),
              }));
            }
          } else {
            warning("No assessments array in response");
            userAssessments.value = [
              {
                session_id: Date.now(),
                assessment_name: "No Assessments Found",
                mode: new Date().toISOString(),
                status: "no_data",
                score: 0,
                easy_count: 0,
                intermediate_count: 0,
                advanced_count: 0,
                session_created: new Date().toISOString(),
                session_completed: null,
                assessment_id: 0,
                assessment_description: "No assessment data available.",
                max_score: 0,
                percentage_score: 0,
              },
            ];
          }

          // Fetch skill performance data
          await fetchSkillPerformance(userId);
        }
      } catch (apiError) {
        error("API error:", { error: apiError });
        setErrorMessage(
          "Error fetching user assessment details. Using fallback data.",
        );
        createFallbackUserData(userObj);
      }
    } catch (selectError) {
      error("Error selecting user:", { error: selectError });
      logError(selectError, "selectUser");
      setErrorMessage(
        getErrorMessage(selectError, "Failed to fetch user assessment details"),
      );
      createFallbackUserData({
        id: userId,
        external_id: "Unknown",
        email: "N/A",
        display_name: "Unknown User",
      });
    } finally {
      userAssessmentsLoading.value = false;
    }
  };

  // Fetch skill performance data
  const fetchSkillPerformance = async (userId) => {
    try {
      let skillResponse;

      if (
        isEmployee.value &&
        currentUserEmail.value &&
        (isNaN(userId) || userId.toString().includes("ldap"))
      ) {
        debug(
          `Using email endpoint for skill performance: ${currentUserEmail.value}`,
        );
        skillResponse = await api.user.getUserSkillPerformanceByEmail(
          currentUserEmail.value,
        );
      } else {
        debug(`Fetching skill performance for user ID: ${userId}`);
        skillResponse = await api.admin.getUserSkillPerformance(userId);
      }

      debug("Skill performance response:", { skillResponse });
      const skillData = extractResponseData(skillResponse);

      if (skillData) {
        if (Array.isArray(skillData)) {
          if (skillData.length > 0) {
            skillPerformanceData.value = skillData.map((skill) => ({
              skill_name: skill.skill_name || "Unknown Skill",
              total_questions_answered: parseInt(
                skill.total_questions_answered || 0,
              ),
              correct_answers: parseInt(skill.correct_answers || 0),
              accuracy_percentage: parseInt(skill.accuracy_percentage || 0),
              total_score: parseInt(skill.total_score || 0),
              avg_score: parseFloat(skill.avg_score || 0),
              easy_correct: parseInt(skill.easy_correct || 0),
              easy_incorrect: parseInt(skill.easy_incorrect || 0),
              intermediate_correct: parseInt(skill.intermediate_correct || 0),
              intermediate_incorrect: parseInt(
                skill.intermediate_incorrect || 0,
              ),
              advanced_correct: parseInt(skill.advanced_correct || 0),
              advanced_incorrect: parseInt(skill.advanced_incorrect || 0),
            }));
          } else {
            skillPerformanceData.value = [createNoDataSkill()];
          }
        } else if (typeof skillData === "object") {
          const skillDataArray =
            skillData.skills || skillData.skill_performance;
          if (Array.isArray(skillDataArray) && skillDataArray.length > 0) {
            skillPerformanceData.value = skillDataArray.map((skill) => ({
              skill_name: skill.skill_name || "Unknown Skill",
              total_questions_answered: parseInt(
                skill.total_questions_answered || 0,
              ),
              correct_answers: parseInt(skill.correct_answers || 0),
              accuracy_percentage: parseInt(skill.accuracy_percentage || 0),
              total_score: parseInt(skill.total_score || 0),
              avg_score: parseFloat(skill.avg_score || 0),
              easy_correct: parseInt(skill.easy_correct || 0),
              easy_incorrect: parseInt(skill.easy_incorrect || 0),
              intermediate_correct: parseInt(skill.intermediate_correct || 0),
              intermediate_incorrect: parseInt(
                skill.intermediate_incorrect || 0,
              ),
              advanced_correct: parseInt(skill.advanced_correct || 0),
              advanced_incorrect: parseInt(skill.advanced_incorrect || 0),
            }));
          } else {
            skillPerformanceData.value = [createNoDataSkill()];
          }
        } else {
          skillPerformanceData.value = [createNoDataSkill()];
        }
      } else {
        skillPerformanceData.value = [createNoDataSkill()];
      }
    } catch (skillError) {
      error("Error fetching skill performance:", { error: skillError });
      logError(skillError, "getUserSkillPerformance");
      setErrorMessage(
        getErrorMessage(skillError, "Failed to fetch skill performance data"),
      );
      skillPerformanceData.value = [createErrorSkill()];
    }
  };

  // Helper functions
  const createNoDataSkill = () => ({
    skill_name: "No Data Available",
    total_questions_answered: 0,
    correct_answers: 0,
    accuracy_percentage: 0,
    total_score: 0,
    avg_score: 0,
    easy_correct: 0,
    easy_incorrect: 0,
    intermediate_correct: 0,
    intermediate_incorrect: 0,
    advanced_correct: 0,
    advanced_incorrect: 0,
  });

  const createErrorSkill = () => ({
    skill_name: "Error Loading Data",
    total_questions_answered: 0,
    correct_answers: 0,
    accuracy_percentage: 0,
    total_score: 0,
    avg_score: 0,
    easy_correct: 0,
    easy_incorrect: 0,
    intermediate_correct: 0,
    intermediate_incorrect: 0,
    advanced_correct: 0,
    advanced_incorrect: 0,
  });

  const createFallbackUserData = (userObj) => {
    userDetails.value = {
      id: userObj.id,
      external_id: userObj.external_id || userObj.id,
      email: userObj.email || "N/A",
      display_name:
        userObj.display_name ||
        userObj.name ||
        userObj.external_id ||
        "Unknown User",
    };

    userAssessments.value = [
      {
        session_id: Date.now(),
        session_code: "FALLBACK",
        session_created: new Date().toISOString(),
        session_completed: null,
        assessment_id: 1,
        assessment_name: "No Assessment Data Available",
        assessment_description:
          "Could not retrieve assessment data from server",
        score: 0,
        max_score: 0,
        percentage_score: 0,
        status: "in_progress",
      },
    ];
  };

  // Go back to user list
  const backToUserList = () => {
    if (isEmployee.value) return;

    selectedUser.value = null;
    userDetails.value = null;
    userAssessments.value = [];
    skillPerformanceData.value = [];
  };

  // Skill performance helpers
  const getBestSkill = () => {
    if (
      !skillPerformanceData.value ||
      skillPerformanceData.value.length === 0
    ) {
      return null;
    }
    return [...skillPerformanceData.value].sort(
      (a, b) => (b.accuracy_percentage || 0) - (a.accuracy_percentage || 0),
    )[0];
  };

  const getWorstSkill = () => {
    if (
      !skillPerformanceData.value ||
      skillPerformanceData.value.length === 0
    ) {
      return null;
    }
    return [...skillPerformanceData.value].sort(
      (a, b) => (a.accuracy_percentage || 0) - (b.accuracy_percentage || 0),
    )[0];
  };

  const getOverallAccuracy = () => {
    if (
      !skillPerformanceData.value ||
      skillPerformanceData.value.length === 0
    ) {
      return 0;
    }
    const totalAccuracy = skillPerformanceData.value.reduce(
      (sum, skill) => sum + (skill.accuracy_percentage || 0),
      0,
    );
    return Math.round(totalAccuracy / skillPerformanceData.value.length);
  };

  const getTotalQuestions = () => {
    if (
      !skillPerformanceData.value ||
      skillPerformanceData.value.length === 0
    ) {
      return 0;
    }
    return skillPerformanceData.value.reduce(
      (sum, skill) => sum + (skill.total_questions_answered || 0),
      0,
    );
  };

  return {
    // State
    message,
    isSuccess,
    users,
    selectedUser,
    userDetails,
    userAssessments,
    skillPerformanceData,
    isEmployee,
    currentUserId,
    currentUserEmail,
    usersLoading,
    userAssessmentsLoading,

    // Methods
    checkUserRole,
    fetchAllUsers,
    findUserIdByEmail,
    selectUser,
    backToUserList,
    getBestSkill,
    getWorstSkill,
    getOverallAccuracy,
    getTotalQuestions,
  };
}
