import { ref } from "vue";

// Create a reactive notification state
const notification = ref(null);

export function useNotification() {
  // Show a notification
  const showNotification = (options) => {
    notification.value = {
      id: Date.now(),
      title: options.title || "Success",
      message: options.message,
      duration: options.duration !== undefined ? options.duration : 5000,
      actionText: options.actionText || "",
      onAction: options.onAction || (() => {}),
      ...options,
    };
  };

  // Clear the notification
  const clearNotification = () => {
    notification.value = null;
  };

  // Show a success notification
  const showSuccess = (message, options = {}) => {
    showNotification({
      title: options.title || "Success",
      message,
      type: "success",
      ...options,
    });
  };

  return {
    notification,
    showNotification,
    clearNotification,
    showSuccess,
  };
}

// Create a singleton instance for global use
const globalNotification = useNotification();
export default globalNotification;
