/**
 * Composable for managing skillwise heatmap reports
 */
import { ref, computed } from "vue";
import { api } from "@/services/api";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { debug } from "@/utils/logger";

export function useSkillwiseReport() {
  const {
    message,
    isSuccess,
    setSuccessMessage,
    setErrorMessage,
    clearMessage,
  } = useMessageHandler();

  // Data states
  const loading = ref(false);
  const users = ref([]);
  const skills = ref([]);
  const skillPerformanceData = ref([]);
  const selectedSkills = ref([]);

  // Computed property to check if we have data to display
  const hasData = computed(() => {
    return (
      skillPerformanceData.value.length > 0 &&
      users.value.length > 0 &&
      skills.value.length > 0
    );
  });

  // Filtered skills based on selection
  const filteredSkills = computed(() => {
    if (!selectedSkills.value.length) {
      return skills.value; // Return all skills if none selected
    }
    return skills.value.filter((skill) =>
      selectedSkills.value.includes(skill.id),
    );
  });

  // Filtered performance data based on selected skills
  const filteredPerformanceData = computed(() => {
    let filteredData = skillPerformanceData.value;

    // Filter by skills if any are selected
    if (selectedSkills.value.length > 0) {
      filteredData = filteredData.filter((item) => {
        // If skill_id is available, use it for filtering
        if (item.skill_id !== undefined) {
          return selectedSkills.value.includes(item.skill_id);
        }

        // Otherwise, try to match by skill name
        const selectedSkillNames = filteredSkills.value.map(
          (skill) => skill.name,
        );
        return selectedSkillNames.includes(item.skill_name);
      });
    }

    return filteredData;
  });

  // Clear skill filter
  const clearSkillFilter = () => {
    selectedSkills.value = [];
  };

  // Remove a single skill from selection
  const removeSkill = (skillId) => {
    selectedSkills.value = selectedSkills.value.filter((id) => id !== skillId);
  };

  // Fetch all required data
  const fetchData = async () => {
    loading.value = true;
    clearMessage();
    selectedSkills.value = []; // Reset skill filter when fetching new data

    try {
      // Fetch all data in a single request using the new endpoint
      const response = await api.admin.getSkillwiseHeatmap();
      const data = extractResponseData(response);

      debug("Heatmap response:", { response });
      debug("Extracted data:", { data });

      if (data) {
        // Set users
        if (data.users && Array.isArray(data.users)) {
          users.value = data.users;
        } else {
          users.value = [];
        }

        // Set skills
        if (data.skills && Array.isArray(data.skills)) {
          skills.value = data.skills;
        } else {
          skills.value = [];
        }

        // Set performance data
        if (data.performance_data && Array.isArray(data.performance_data)) {
          skillPerformanceData.value = data.performance_data;
        } else {
          skillPerformanceData.value = [];
        }

        if (skillPerformanceData.value.length > 0) {
          setSuccessMessage("Data loaded successfully");
        } else {
          setErrorMessage("No skill performance data available");
        }
      } else {
        setErrorMessage("Invalid response format from server");
        users.value = [];
        skills.value = [];
        skillPerformanceData.value = [];
      }
    } catch (error) {
      logError(error, "fetchData");
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(errorInfo.message || "Failed to fetch data");
      users.value = [];
      skills.value = [];
      skillPerformanceData.value = [];
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    message,
    isSuccess,
    loading,
    users,
    skills,
    skillPerformanceData,
    selectedSkills,
    hasData,
    filteredSkills,
    filteredPerformanceData,

    // Methods
    clearSkillFilter,
    removeSkill,
    fetchData,
  };
}
