import { ref, computed } from 'vue'
import { useApiRequest } from './useApiRequest'
import { useNavigation } from './useNavigation'
import { useNotification } from './useNotification'
import { logError, debug, warning } from '@/utils/logger'
import { getErrorMessage } from '@/utils/errorHandling'

export function useAssessmentCreation() {
  const { api } = useApiRequest()
  const navigation = useNavigation()
  const { showSuccess, setErrorMessage, setErrorMessageWithScroll } = useNotification()

  // Form state
  const assessmentName = ref('')
  const assessmentDescription = ref('')
  const selectedSkillIds = ref([])
  const questionSelectionMode = ref('dynamic')
  const assessmentDuration = ref(20)
  const isLoading = ref(false)
  const createdAssessmentDetails = ref(null)

  // Skills data
  const skills = ref([])
  const hasInteractedWithSkills = ref(false)

  // Computed properties
  const isFormValid = computed(() => {
    return assessmentName.value.trim() && 
           assessmentDescription.value.trim() && 
           selectedSkillIds.value.length > 0
  })

  // Fetch skills from API
  const fetchSkills = async () => {
    try {
      isLoading.value = true
      const response = await api.admin.getSkills({ limit: 100, offset: 0 })
      const skillsData = extractResponseData(response)

      if (Array.isArray(skillsData)) {
        debug('Fetched skills from API', {
          skillsCount: skillsData.length,
          sampleData: skillsData.slice(0, 2)
        })

        const filteredSkills = skillsData.filter((skill) => {
          if (!skill) {
            warning('Filtering out null/undefined skill')
            return false
          }
          return true
        })

        skills.value = filteredSkills
        debug('Skills loaded successfully', { count: filteredSkills.length })
      } else {
        warning('Skills data is not an array', { skillsData })
        skills.value = []
      }
    } catch (error) {
      logError(error, 'fetchSkills')
      setErrorMessage(getErrorMessage(error, 'Failed to load skills'))
      skills.value = []
    } finally {
      isLoading.value = false
    }
  }

  // Helper functions
  const logAssessmentCreationData = (username, validSkillIds, durationValue) => {
    debug('Creating assessment with data:', {
      assessmentName: assessmentName.value,
      assessmentDescription: assessmentDescription.value,
      username,
      skillIds: validSkillIds,
      questionSelectionMode: questionSelectionMode.value,
      duration: durationValue
    })
  }

  const callCreateAssessmentApi = async (username, validSkillIds, durationValue) => {
    return await api.admin.createAssessment({
      quiz_name: assessmentName.value.trim(),
      topic: assessmentDescription.value.trim(),
      user_id: username.trim(),
      skill_ids: validSkillIds,
      question_selection_mode: questionSelectionMode.value,
      duration: durationValue,
      create_single_assessment: true
    })
  }

  // Create assessment
  const createAssessment = async () => {
    if (!isFormValid.value) {
      setErrorMessage('Please fill in all required fields')
      return false
    }

    try {
      isLoading.value = true
      
      const username = 'admin' // This should come from auth context
      const validSkillIds = selectedSkillIds.value.filter(id => id != null)
      const durationValue = Math.max(1, Math.floor(Number(assessmentDuration.value) || 20))

      logAssessmentCreationData(username, validSkillIds, durationValue)

      const response = await callCreateAssessmentApi(username, validSkillIds, durationValue)
      const responseData = extractResponseData(response)

      createdAssessmentDetails.value = responseData

      showSuccess('Assessment created successfully! Go to Generate Sessions', {
        duration: 7000,
        actionText: 'Go to Generate Sessions',
        onAction: () => navigation.navigateTo('/generate-sessions')
      })

      return true
    } catch (error) {
      logError(error, 'createAssessment')
      setErrorMessage(getErrorMessage(error, 'Failed to create assessment'))
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Reset form
  const resetForm = () => {
    assessmentName.value = ''
    assessmentDescription.value = ''
    selectedSkillIds.value = []
    assessmentDuration.value = 20
    questionSelectionMode.value = 'dynamic'
    createdAssessmentDetails.value = null
  }

  return {
    // State
    assessmentName,
    assessmentDescription,
    selectedSkillIds,
    questionSelectionMode,
    assessmentDuration,
    isLoading,
    createdAssessmentDetails,
    skills,
    hasInteractedWithSkills,
    
    // Computed
    isFormValid,
    
    // Methods
    fetchSkills,
    createAssessment,
    resetForm
  }
}
