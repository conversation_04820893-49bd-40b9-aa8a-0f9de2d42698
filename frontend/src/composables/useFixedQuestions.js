import { ref, computed } from 'vue'
import { useApiRequest } from './useApiRequest'
import { useNotification } from './useNotification'
import { extractResponseData } from '@/utils/apiResponseHandler'
import { logError, debug, warning } from '@/utils/logger'
import { getErrorMessage } from '@/utils/errorHandling'

export function useFixedQuestions() {
  const { api } = useApiRequest()
  const { setErrorMessage, setErrorMessageWithScroll } = useNotification()

  // State
  const availableQuestions = ref([])
  const questionIds = ref('')
  const isLoadingQuestions = ref(false)
  const manualQuestionCounts = ref({
    easy: 10,
    intermediate: 10,
    advanced: 10
  })

  // Computed properties
  const getTotalManualQuestions = computed(() => {
    return manualQuestionCounts.value.easy + 
           manualQuestionCounts.value.intermediate + 
           manualQuestionCounts.value.advanced
  })

  const getSelectedQuestionCount = computed(() => {
    if (!questionIds.value) return 0
    return questionIds.value.split(',').filter(id => id.trim()).length
  })

  const getQuestionsByDifficulty = computed(() => {
    const grouped = {
      easy: [],
      intermediate: [],
      advanced: []
    }

    availableQuestions.value.forEach(question => {
      const difficulty = question.difficulty?.toLowerCase() || 'easy'
      if (grouped[difficulty]) {
        grouped[difficulty].push(question)
      }
    })

    return grouped
  })

  // Fetch questions for assessment
  const fetchAssessmentQuestions = async (assessmentId) => {
    if (!assessmentId) return

    isLoadingQuestions.value = true
    try {
      const questionsResponse = await api.admin.getAssessmentQuestions(assessmentId)
      const questionData = extractResponseData(questionsResponse)
      availableQuestions.value = questionData?.questions || []
    } catch (error) {
      logError(error, 'fetchAssessmentQuestions')
      setErrorMessage(
        getErrorMessage(error, 'Failed to fetch questions for this assessment')
      )
    } finally {
      isLoadingQuestions.value = false
    }
  }

  // Fetch questions for selected skills
  const fetchQuestionsForSkills = async (skillIds) => {
    if (!skillIds || skillIds.length === 0) {
      availableQuestions.value = []
      return
    }

    isLoadingQuestions.value = true
    try {
      const allQuestions = []
      for (const skillId of skillIds) {
        try {
          const response = await api.admin.getSkillQuestions(skillId)
          const responseData = extractResponseData(response)

          if (responseData && responseData.questions) {
            const questionsWithSkillName = responseData.questions.map(question => ({
              ...question,
              skill_name: responseData.skill_name
            }))
            allQuestions.push(...questionsWithSkillName)
          }
        } catch (error) {
          warning(`Failed to fetch questions for skill ${skillId}:`, error)
        }
      }

      availableQuestions.value = allQuestions
      debug('Fetched questions for skills', {
        skillCount: skillIds.length,
        totalQuestions: allQuestions.length
      })
    } catch (error) {
      logError(error, 'fetchQuestionsForSkills')
      setErrorMessage(getErrorMessage(error, 'Failed to fetch questions'))
    } finally {
      isLoadingQuestions.value = false
    }
  }

  // Add fixed questions to assessment
  const addFixedQuestions = async (assessmentId, assessmentName) => {
    if (!questionIds.value.trim()) {
      setErrorMessage('Please select at least one question')
      return false
    }

    const selectedIds = questionIds.value
      .split(',')
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id))

    if (selectedIds.length === 0) {
      setErrorMessage('Please select valid questions')
      return false
    }

    isLoadingQuestions.value = true
    try {
      await api.admin.addFinalQuestions({
        assessment_id: parseInt(assessmentId),
        question_ids: selectedIds,
        quiz_name: assessmentName || 'Assessment',
        question_distribution: {
          easy: manualQuestionCounts.value.easy,
          intermediate: manualQuestionCounts.value.intermediate,
          advanced: manualQuestionCounts.value.advanced,
          total: getTotalManualQuestions.value
        }
      })

      // Clear selection after success
      questionIds.value = ''
      return true
    } catch (error) {
      logError(error, 'addFixedQuestions')
      setErrorMessageWithScroll(
        getErrorMessage(error, 'An unexpected error occurred while assigning questions')
      )
      return false
    } finally {
      isLoadingQuestions.value = false
    }
  }

  // Reset questions data
  const resetQuestions = () => {
    availableQuestions.value = []
    questionIds.value = ''
    manualQuestionCounts.value = { easy: 10, intermediate: 10, advanced: 10 }
  }

  return {
    // State
    availableQuestions,
    questionIds,
    isLoadingQuestions,
    manualQuestionCounts,
    
    // Computed
    getTotalManualQuestions,
    getSelectedQuestionCount,
    getQuestionsByDifficulty,
    
    // Methods
    fetchAssessmentQuestions,
    fetchQuestionsForSkills,
    addFixedQuestions,
    resetQuestions
  }
}
