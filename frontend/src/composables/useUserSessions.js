/**
 * Composable for managing user sessions
 */
import { ref, computed } from "vue";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import { extractResponseData } from "@/utils/apiResponseHandler";
import { decodeSessionCodes, getDisplaySessionCode } from "@/utils/hashIds";
import { useLoadingState, useSearch } from "./useUIState";
import { useNavigation } from "./useNavigation";
import { debug, warning, error } from "@/utils/logger";

export function useUserSessions() {
  const loadingState = useLoadingState(true);
  const search = useSearch({ minLength: 2, debounceMs: 300 });
  const navigation = useNavigation();
  const {
    message,
    isSuccess,
    setErrorMessage,
    setSuccessMessage,
    clearMessage,
  } = useMessageHandler();

  const allSessions = ref([]);

  // Aliases
  const { isLoading } = loadingState;
  const { searchQuery } = search;

  // Computed properties
  const pendingSessions = computed(() =>
    allSessions.value.filter(
      (session) =>
        (session.status === "pending" ||
          session.status === "created" ||
          !session.status) &&
        session.status !== "in_progress" &&
        session.status !== "started",
    ),
  );

  const inProgressSessions = computed(() =>
    allSessions.value.filter(
      (session) =>
        session.status === "in_progress" || session.status === "started",
    ),
  );

  const completedSessions = computed(() =>
    allSessions.value.filter(
      (session) =>
        session.status === "completed" || session.status === "finished",
    ),
  );

  const filteredPendingSessions = computed(() => {
    if (!searchQuery.value) return pendingSessions.value;
    const query = searchQuery.value.toLowerCase();
    return pendingSessions.value.filter((session) => {
      const displayCode = getDisplaySessionCode(session);
      return (
        (session.id_hash && session.id_hash.toLowerCase().includes(query)) ||
        (session.session_code &&
          session.session_code.toLowerCase().includes(query)) ||
        (displayCode && displayCode.toLowerCase().includes(query)) ||
        (session.assessment_name &&
          session.assessment_name.toLowerCase().includes(query))
      );
    });
  });

  const filteredInProgressSessions = computed(() => {
    if (!searchQuery.value) return inProgressSessions.value;
    const query = searchQuery.value.toLowerCase();
    return inProgressSessions.value.filter((session) => {
      const displayCode = getDisplaySessionCode(session);
      return (
        (session.id_hash && session.id_hash.toLowerCase().includes(query)) ||
        (session.session_code &&
          session.session_code.toLowerCase().includes(query)) ||
        (displayCode && displayCode.toLowerCase().includes(query)) ||
        (session.assessment_name &&
          session.assessment_name.toLowerCase().includes(query))
      );
    });
  });

  const filteredCompletedSessions = computed(() => {
    if (!searchQuery.value) return completedSessions.value;
    const query = searchQuery.value.toLowerCase();
    return completedSessions.value.filter((session) => {
      const displayCode = getDisplaySessionCode(session);
      return (
        (session.id_hash && session.id_hash.toLowerCase().includes(query)) ||
        (session.session_code &&
          session.session_code.toLowerCase().includes(query)) ||
        (displayCode && displayCode.toLowerCase().includes(query)) ||
        (session.assessment_name &&
          session.assessment_name.toLowerCase().includes(query))
      );
    });
  });

  // Methods
  const getUserEmail = () => {
    try {
      const userInfoStr = localStorage.getItem("user_info");
      if (!userInfoStr) {
        warning("No user info found in localStorage");
        return "<EMAIL>";
      }
      const userInfo = JSON.parse(userInfoStr);
      const email = userInfo.email;
      if (!email) {
        warning("No email found in user info");
        return "<EMAIL>";
      }
      return email;
    } catch (err) {
      error("Error getting user email", { err });
      return "<EMAIL>";
    }
  };

  const fetchUserSessions = async () => {
    isLoading.value = true;
    clearMessage();
    try {
      const userEmail = getUserEmail();
      debug("Fetching sessions for user", { userEmail });
      const response = await api.user.getUserSessions(userEmail);
      const responseData = extractResponseData(response);
      if (responseData && responseData.sessions) {
        const sessions = responseData.sessions || [];
        await decodeSessionCodes(sessions);
        allSessions.value = sessions;
        if (allSessions.value.length === 0) {
          setSuccessMessage(
            "No sessions found. You have not been assigned any assessments yet.",
          );
        }
      } else {
        error("Unexpected response format", { response });
        setErrorMessage(
          "Received an unexpected response format from the server",
        );
        allSessions.value = [];
      }
    } catch (err) {
      error("Error fetching sessions", { err });
      logError(err, "fetchUserSessions");
      setErrorMessage(getErrorMessage(err, "Failed to fetch sessions"));
    } finally {
      isLoading.value = false;
    }
  };

  const startSession = async (session) => {
    const isInProgress =
      session.status === "in_progress" || session.status === "started";
    const sessionCode = session.id_hash || session.session_code;
    if (!sessionCode) {
      setErrorMessage("Invalid session code");
      return;
    }
    try {
      isLoading.value = true;
      const userEmail = getUserEmail();
      const userInfoStr = localStorage.getItem("user_info");
      let username = "";
      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        username =
          userInfo.username || userInfo.name || userEmail.split("@")[0];
      }
      localStorage.setItem(
        "quiz_auto_fill",
        JSON.stringify({
          username: username,
          email: userEmail,
          session_code: sessionCode,
          assessment_name: session.assessment_name,
          auto_start: true,
          timestamp: new Date().getTime(),
        }),
      );
      const path = `/quiz/${sessionCode}`;
      const query = {
        directStart: "true",
        autoFill: "true",
        t: new Date().getTime(),
      };
      if (isInProgress) {
        query.resume = "true";
      }
      navigation.router.push({ path, query });
    } catch (err) {
      logError(err, "startSession");
      setErrorMessage(getErrorMessage(err, "Failed to start session"));
    } finally {
      isLoading.value = false;
    }
  };

  const getSessionIdentifier = async (session) => {
    // Try to get session identifier in order of preference:
    // 1. Direct session_code (if still present)
    // 2. Direct code field
    // 3. Already decoded session code (from decodeSessionCodes)
    // 4. Decode session code from id_hash
    // 5. Direct id field as fallback
    let sessionId =
      session.session_code || session.code || session._decodedSessionCode;

    if (!sessionId && session.id_hash) {
      // Try to decode the session code from the hash
      try {
        const { decodeSessionCodeFromHash } = await import("@/utils/hashIds");
        sessionId = await decodeSessionCodeFromHash(session.id_hash);
        debug("Decoded session code from hash:", sessionId);
      } catch (error) {
        warning("Failed to decode session code from hash:", error);
      }
    }

    // Fallback to direct id if nothing else works
    if (!sessionId) {
      sessionId = session.id;
    }

    return sessionId;
  };

  return {
    isLoading,
    message,
    isSuccess,
    searchQuery,
    pendingSessions,
    inProgressSessions,
    completedSessions,
    filteredPendingSessions,
    filteredInProgressSessions,
    filteredCompletedSessions,
    totalPendingSessions: computed(() => pendingSessions.value.length),
    totalCompletedSessions: computed(() => completedSessions.value.length),
    fetchUserSessions,
    startSession,
    getSessionIdentifier,
  };
}
