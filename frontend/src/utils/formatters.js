/**
 * Common formatting utilities for the application
 */
import { warning, logError } from "@/utils/logger";

/**
 * Format date string to localized date and time
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date string or "N/A"
 */
export const formatDate = (dateString) => {
  if (!dateString) return "N/A";

  // <PERSON>le special case for "dynamic" mode
  if (dateString === "dynamic") {
    return "Dynamic";
  }

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return as is without logging
    }

    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  } catch (error) {
    logError(error, "formatDate");
    return dateString; // Return as is without logging
  }
};

/**
 * Format date for detailed display with more options
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date string
 */
export const formatDateDetailed = (dateString) => {
  if (!dateString) return "N/A";

  if (dateString === "dynamic") {
    return "Dynamic";
  }

  try {
    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      return dateString;
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch (error) {
    logError(error, "formatDateDetailed");
    return dateString;
  }
};

/**
 * Simple date formatting for modals
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date string
 */
export const formatDateSimple = (dateString) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleString();
};

/**
 * Get assessment type display name
 * @param {string} questionSelectionMode - The question selection mode
 * @returns {string} - Display name for the assessment type
 */
export const getAssessmentType = (questionSelectionMode) => {
  if (!questionSelectionMode) return "N/A";
  const mode = String(questionSelectionMode).toLowerCase().trim();
  switch (mode) {
    case "dynamic":
      return "Dynamic";
    case "fixed":
      return "Fixed";
    default:
      if (import.meta.env.DEV) {
        warning("Unexpected question_selection_mode value:", {
          mode,
          original: questionSelectionMode,
        });
      }
      return mode.charAt(0).toUpperCase() + mode.slice(1);
  }
};

/**
 * Get CSS classes for assessment type badge
 * @param {string} questionSelectionMode - The question selection mode
 * @returns {string} - CSS classes for the badge
 */
export const getAssessmentTypeClass = (questionSelectionMode) => {
  if (!questionSelectionMode) {
    return "bg-gray-500/10 text-gray-400 border border-gray-500/20";
  }
  const mode = String(questionSelectionMode).toLowerCase().trim();
  switch (mode) {
    case "dynamic":
      return "bg-purple-500/10 text-purple-400 border border-purple-500/20";
    case "fixed":
      return "bg-orange-500/10 text-orange-400 border border-orange-500/20";
    default:
      return "bg-blue-500/10 text-blue-400 border border-blue-500/20";
  }
};
