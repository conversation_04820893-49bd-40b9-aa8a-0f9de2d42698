<template>
  <div
    class="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800 p-6"
  >
    <!-- Assessment Selection -->
    <div class="space-y-6">
      <div>
        <Label for="assessmentSelect" class="text-white font-medium mb-2 block"
          >Select Assessment</Label
        >
        <Select
          id="assessmentSelect"
          v-model="selectedAssessmentId"
          name="assessmentSelect"
          @update:model-value="onAssessmentChange"
        >
          <SelectTrigger
            variant="small"
            class="bg-gray-800/70 border-gray-700 text-white font-bold"
          >
            <SelectValue variant="small" placeholder="Choose an assessment" />
          </SelectTrigger>
          <SelectContent
            id="assessmentSelectContent"
            class="bg-gray-800 border border-gray-700 max-h-60 overflow-y-auto"
          >
            <template
              v-for="item in assessments || []"
              :key="item?.id_hash || item?.id || item?.name || Math.random()"
            >
              <SelectItem
                v-if="item && (item.id_hash || item.id)"
                :value="(item?.id_hash || item?.id)?.toString() || ''"
                class="text-white hover:bg-gray-700"
              >
                {{ item?.name || "Unnamed Assessment" }}
              </SelectItem>
            </template>
            <div
              v-if="!assessments || assessments.length === 0"
              class="px-4 py-3 text-gray-400 text-sm"
            >
              No assessments available
            </div>
          </SelectContent>
        </Select>
        <p class="text-xs text-gray-400 mt-1">
          Select an assessment to view its skills and questions
        </p>
      </div>

      <!-- Error/Success message -->
      <Alert
        v-if="message"
        :variant="isSuccess ? 'success' : 'error'"
        class="mt-4"
      >
        <AlertDescription>{{ message }}</AlertDescription>
      </Alert>
    </div>

    <!-- Assessment Details Section -->
    <div v-if="selectedAssessment && assessmentDetails" class="mt-8">
      <h3 class="text-lg font-semibold text-white mb-6">Assessment Details</h3>

      <!-- Assessment Info -->
      <div class="bg-gray-800/50 rounded-lg border border-gray-700 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-gray-400 text-sm">Assessment Name</p>
            <p class="text-white font-medium">
              {{ assessmentDetails.name }}
            </p>
          </div>
          <div>
            <p class="text-gray-400 text-sm">Mode</p>
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="
                assessmentDetails.question_selection_mode === 'fixed'
                  ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                  : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
              "
            >
              {{
                assessmentDetails.question_selection_mode === "fixed"
                  ? "Fixed"
                  : "Dynamic"
              }}
            </span>
          </div>
          <div>
            <p class="text-gray-400 text-sm">Total Questions</p>
            <p class="text-white font-medium">
              {{ assessmentDetails.total_questions || "N/A" }}
            </p>
          </div>
        </div>
      </div>

      <!-- Skills Section -->
      <div
        v-if="assessmentDetails.skills && assessmentDetails.skills.length > 0"
        class="mb-6"
      >
        <h4 class="text-md font-semibold text-white mb-4">Associated Skills</h4>
        <div class="bg-gray-800/50 rounded-lg border border-gray-700 p-4">
          <div class="flex flex-wrap gap-2">
            <span
              v-for="skill in assessmentDetails.skills"
              :key="skill.id"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30"
            >
              {{ skill.name }}
            </span>
          </div>
        </div>
      </div>

      <!-- Questions Section -->
      <div
        v-if="
          sortedQuestions.length > 0 ||
          (assessmentDetails?.available_questions &&
            assessmentDetails.available_questions.length > 0) ||
          (reportQuestions && reportQuestions.length > 0)
        "
      >
        <div class="flex justify-between items-center mb-4">
          <h4 class="text-md font-semibold text-white">
            <template
              v-if="
                selectedAssessment &&
                selectedAssessment.question_selection_mode === 'fixed'
              "
            >
              All Questions
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 ml-2"
              >
                {{
                  selectedLevel
                    ? sortedQuestions.length
                    : assessmentDetails?.available_questions?.length ||
                      reportQuestions?.length ||
                      0
                }}
              </span>
            </template>
            <template v-else>
              Attended Questions
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 ml-2"
              >
                {{ sortedQuestions.length }}
              </span>
            </template>
            <span
              v-if="assessmentDetails.question_stats"
              class="ml-3 inline-flex items-center space-x-2"
            >
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-green-500/20 text-green-400 border border-green-500/30"
              >
                {{ assessmentDetails.question_stats.available.easy }}
              </span>
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
              >
                {{ assessmentDetails.question_stats.available.intermediate }}
              </span>
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-orange-500/20 text-orange-400 border border-orange-500/30"
              >
                {{ assessmentDetails.question_stats.available.advanced }}
              </span>
            </span>
          </h4>

          <!-- Level Filter Buttons -->
          <div class="flex items-center">
            <span class="text-white text-sm mr-2">Filter by Level:</span>
            <div class="flex space-x-2">
              <button
                class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                :class="
                  selectedLevel === 'easy'
                    ? 'bg-green-500/40 text-green-300 border border-green-500/50'
                    : 'bg-green-500/10 text-green-400 border border-green-500/20 hover:bg-green-500/20'
                "
                @click="toggleLevelFilter('easy')"
              >
                <span
                  class="inline-block w-2 h-2 rounded-full bg-green-400 mr-2"
                />
                Easy
              </button>
              <button
                class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                :class="
                  selectedLevel === 'intermediate'
                    ? 'bg-yellow-500/40 text-yellow-300 border border-yellow-500/50'
                    : 'bg-yellow-500/10 text-yellow-400 border border-yellow-500/20 hover:bg-yellow-500/20'
                "
                @click="toggleLevelFilter('intermediate')"
              >
                <span
                  class="inline-block w-2 h-2 rounded-full bg-yellow-400 mr-2"
                />
                Intermediate
              </button>
              <button
                class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                :class="
                  selectedLevel === 'advanced'
                    ? 'bg-orange-500/40 text-orange-300 border border-orange-500/50'
                    : 'bg-orange-500/10 text-orange-400 border border-orange-500/20 hover:bg-orange-500/20'
                "
                @click="toggleLevelFilter('advanced')"
              >
                <span
                  class="inline-block w-2 h-2 rounded-full bg-orange-400 mr-2"
                />
                Advanced
              </button>
            </div>
          </div>
        </div>

        <!-- Questions List -->
        <div
          class="bg-gray-800/50 rounded-lg border border-gray-700 overflow-hidden"
        >
          <!-- Loading indicator for question statistics -->
          <div
            v-if="questionStatsLoading"
            class="flex items-center justify-center py-8"
          >
            <SpinnerIcon class="w-6 h-6 mr-2 text-cyan-500" />
            <span class="text-white">Loading question statistics...</span>
          </div>

          <div v-else class="max-h-96 overflow-y-auto">
            <table class="w-full">
              <thead class="bg-gray-700/50 sticky top-0">
                <tr>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Skill
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Level
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Question
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Total Users
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Correct %
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="question in sortedQuestions"
                  :key="question.que_id"
                  class="border-t border-gray-700/50 hover:bg-gray-700/30"
                >
                  <td class="px-4 py-3">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30"
                    >
                      {{ question.skill_name }}
                    </span>
                  </td>
                  <td class="px-4 py-3">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      :class="{
                        'bg-green-500/20 text-green-400 border border-green-500/30':
                          question.level === 'easy',
                        'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30':
                          question.level === 'intermediate',
                        'bg-orange-500/20 text-orange-400 border border-orange-500/30':
                          question.level === 'advanced',
                      }"
                    >
                      {{ question.level }}
                    </span>
                  </td>
                  <td class="px-4 py-3 text-gray-300 text-sm max-w-xs">
                    <div class="truncate" :title="question.question">
                      {{ question.question }}
                    </div>
                  </td>
                  <td class="px-4 py-3 text-white text-sm">
                    {{ calculateTotalUsers(question) }}
                  </td>
                  <td class="px-4 py-3 text-white text-sm">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      :class="{
                        'bg-red-500/20 text-red-400 border border-red-500/30':
                          (question.correct_percentage || 0) < 50,
                        'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30':
                          (question.correct_percentage || 0) >= 50 &&
                          (question.correct_percentage || 0) < 75,
                        'bg-green-500/20 text-green-400 border border-green-500/30':
                          (question.correct_percentage || 0) >= 75,
                      }"
                    >
                      {{ (question.correct_percentage || 0).toFixed(1) }}%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- No Questions Message -->
      <div
        v-else-if="selectedAssessment && assessmentDetails"
        class="text-center py-8"
      >
        <div
          class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-700/50 flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-8 w-8 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <template
          v-if="
            (!assessmentDetails?.available_questions ||
              assessmentDetails.available_questions.length === 0) &&
            (!reportQuestions || reportQuestions.length === 0)
          "
        >
          <h3 class="text-lg font-medium text-white mb-2">
            No Questions Available
          </h3>
          <p class="text-gray-400">
            This assessment has no associated questions.
          </p>
        </template>
        <template
          v-else-if="
            selectedAssessment &&
            selectedAssessment.question_selection_mode === 'dynamic' &&
            (!reportQuestions || reportQuestions.length === 0)
          "
        >
          <h3 class="text-lg font-medium text-white mb-2">
            No Attended Questions
          </h3>
          <p class="text-gray-400">
            No users have attended questions from this dynamic assessment yet.
          </p>
        </template>
      </div>

      <!-- Generate Report Button -->
      <div class="flex justify-end mt-4">
        <Button
          :disabled="assessmentWiseLoading"
          variant="generalAction"
          size="backButton"
          @click="generateAssessmentWiseReport"
        >
          <SpinnerIcon v-if="assessmentWiseLoading" class="w-3 h-3 mr-1" />
          {{ assessmentWiseLoading ? "Generating..." : "Generate Report" }}
        </Button>
      </div>
    </div>

    <!-- Download Cards for Assessment-wise Report -->
    <div v-if="assessmentWiseReportGenerated" class="mt-8">
      <h3 class="text-lg font-semibold text-white mb-4">Download Reports</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Base Report Download Card -->
        <div
          v-if="assessmentWiseReports.baseReportUrl"
          class="bg-gray-800/50 rounded-lg border border-cyan-500/30 p-4 hover:bg-gray-800/70 transition-colors"
        >
          <div class="flex items-center">
            <div class="bg-cyan-500/10 p-2 rounded-lg mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-cyan-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-white font-medium">Base Report</p>
              <p class="text-xs text-gray-400">
                Contains detailed assessment data for
                {{ selectedAssessment?.name || "selected assessment" }}
              </p>
            </div>
            <Button
              :as="'a'"
              :href="assessmentWiseReports.baseReportUrl"
              download
              variant="generalAction"
              size="backButton"
              class="bg-cyan-600 hover:bg-cyan-700"
            >
              Download
            </Button>
          </div>
        </div>

        <!-- Score Report Download Card -->
        <div
          v-if="assessmentWiseReports.scoreReportUrl"
          class="bg-gray-800/50 rounded-lg border border-cyan-500/30 p-4 hover:bg-gray-800/70 transition-colors"
        >
          <div class="flex items-center">
            <div class="bg-cyan-500/10 p-2 rounded-lg mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-cyan-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-white font-medium">Score Report</p>
              <p class="text-xs text-gray-400">
                Contains score analysis and statistics for
                {{ selectedAssessment?.name || "selected assessment" }}
              </p>
            </div>
            <Button
              :as="'a'"
              :href="assessmentWiseReports.scoreReportUrl"
              download
              variant="generalAction"
              size="backButton"
              class="bg-cyan-600 hover:bg-cyan-700"
            >
              Download
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SpinnerIcon } from "@/components/icons";
import { useAssessmentReport } from "@/composables";

// Composables
const {
  message,
  isSuccess,
  assessments,
  selectedAssessmentId,
  selectedAssessment,
  assessmentDetails,
  selectedLevel,
  assessmentWiseLoading,
  questionStatsLoading,
  assessmentWiseReportGenerated,
  assessmentWiseReports,
  reportQuestions,
  sortedQuestions,
  fetchAssessments,
  onAssessmentChange,
  generateAssessmentWiseReport,
  calculateTotalUsers,
  toggleLevelFilter,
} = useAssessmentReport();

// Initialize component
onMounted(() => {
  fetchAssessments();
});
</script>
