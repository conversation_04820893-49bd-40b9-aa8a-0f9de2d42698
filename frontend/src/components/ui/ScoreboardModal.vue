<template>
  <!-- Scoreboard Modal -->
  <div
    v-if="isOpen"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm animate-fadeIn"
    @click="handleBackdropClick"
  >
    <div
      class="relative w-full max-w-4xl max-h-[90vh] bg-phantom-dark border border-white/10 rounded-lg shadow-glow-lg overflow-hidden flex flex-col animate-scaleIn"
      @click.stop
    >
      <!-- Modal Header -->
      <div
        class="p-6 border-b border-white/10 flex justify-between items-center sticky top-0 bg-phantom-dark z-10 shadow-md backdrop-blur-sm"
      >
        <h3 class="text-2xl font-bold text-white flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-3 text-phantom-blue"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          Quiz Results
        </h3>
        <button
          class="text-white/60 hover:text-white transition-colors duration-200 p-2 hover:bg-white/10 rounded-lg"
          @click="close"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Content - Scrollable Area -->
      <div class="overflow-y-auto p-6 flex-grow custom-scrollbar-modal">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center py-12">
          <div
            class="animate-spin rounded-full h-12 w-12 border-b-2 border-phantom-blue"
          ></div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <p class="text-red-400 mb-4">{{ error }}</p>
          <button
            class="btn-phantom-secondary px-4 py-2"
            @click="() => loadSessionResults(sessionId)"
          >
            Try Again
          </button>
        </div>

        <!-- Results Content -->
        <div v-else-if="sessionResults">
          <!-- Session Info -->
          <div class="mb-6 text-center">
            <h2 class="text-2xl font-bold text-white mb-2">
              {{ sessionResults.session_info.assessment_name }}
            </h2>
            <p class="text-white/70">
              Session:
              <span class="font-mono text-phantom-blue">{{
                sessionResults.session_info.session_code
              }}</span>
            </p>
            <p class="text-white/70">
              Completed:
              {{ formatDateSimple(sessionResults.session_info.completed_at) }}
            </p>
          </div>

          <!-- Score Summary -->
          <div
            class="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10 mb-6"
          >
            <div class="grid grid-cols-3 gap-4 text-center">
              <div>
                <div class="text-3xl font-bold text-phantom-blue">
                  {{ sessionResults.score_summary.correct_answers }}
                </div>
                <div class="text-white/70">Correct Answers</div>
              </div>
              <div>
                <div class="text-3xl font-bold text-white">
                  {{ sessionResults.score_summary.questions_attempted }}
                </div>
                <div class="text-white/70">
                  {{
                    sessionResults.session_info.question_selection_mode ===
                    "dynamic"
                      ? "Questions Attempted"
                      : "Total Questions"
                  }}
                </div>
              </div>
              <div>
                <div class="text-3xl font-bold text-phantom-indigo">
                  {{ sessionResults.score_summary.final_score.toFixed(1) }}
                </div>
                <div class="text-white/70">Total Score</div>
              </div>
            </div>
          </div>

          <!-- Performance Level -->
          <div class="text-center mb-6">
            <div
              class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold"
              :class="getPerformanceLevelClass()"
            >
              {{ getPerformanceLevel() }}
            </div>
          </div>

          <!-- Detailed Results Button -->
          <div v-if="sessionResults.answered_questions.length > 0" class="mb-6">
            <button
              class="w-full py-4 px-6 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-phantom-blue/50 rounded-lg transition-all duration-200 flex items-center justify-center"
              @click="toggleDetailedResults"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 text-phantom-blue"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <span class="text-lg font-semibold btn-text-visible">
                {{ showDetailedResults ? "Hide" : "View" }} Detailed Results
              </span>
            </button>
          </div>

          <!-- Detailed Results -->
          <div
            v-if="
              showDetailedResults &&
              sessionResults.answered_questions.length > 0
            "
            class="space-y-4"
          >
            <div
              v-for="(question, index) in sessionResults.answered_questions"
              :key="index"
              class="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10"
            >
              <div class="flex justify-between items-start mb-2">
                <h4 class="text-white font-medium">
                  {{ index + 1 }}. {{ question.question }}
                </h4>
                <span
                  class="ml-2 px-2 py-1 rounded-full text-xs font-semibold"
                  :class="
                    question.isCorrect
                      ? 'bg-green-400/10 text-green-300'
                      : 'bg-red-400/10 text-red-300'
                  "
                >
                  {{ question.isCorrect ? "Correct" : "Incorrect" }}
                </span>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mt-3">
                <div
                  v-for="(option, key) in question.options"
                  :key="key"
                  class="p-3 rounded-lg border text-sm flex items-center"
                  :class="getResultOptionClass(key, question)"
                >
                  <span
                    class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold mr-2"
                    :class="getResultOptionIconClass(key, question)"
                  >
                    {{ key.toUpperCase() }}
                  </span>
                  <span>{{ option }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useSessionResults } from "@/composables";
import { formatDateSimple } from "@/utils/formatters";

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  sessionId: {
    type: String,
    default: "",
  },
});

// Emits
const emit = defineEmits(["close"]);

// Composables
const {
  isLoading,
  error,
  sessionResults,
  loadSessionResults,
  getPerformanceLevel,
  getPerformanceLevelClass,
  getResultOptionClass,
  getResultOptionIconClass,
  resetResults,
} = useSessionResults();

// Local state
const showDetailedResults = ref(false);

// Methods
const close = () => {
  emit("close");
  // Reset state when closing
  resetResults();
  showDetailedResults.value = false;
};

const handleBackdropClick = (event) => {
  if (event.target === event.currentTarget) {
    close();
  }
};

const toggleDetailedResults = () => {
  showDetailedResults.value = !showDetailedResults.value;
};

// Watch for sessionId changes to load results
import { watch } from "vue";

watch(
  () => props.sessionId,
  (newSessionId) => {
    if (newSessionId && props.isOpen) {
      loadSessionResults(newSessionId);
    }
  },
  { immediate: true },
);

watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen && props.sessionId) {
      loadSessionResults(props.sessionId);
    }
  },
);
</script>
