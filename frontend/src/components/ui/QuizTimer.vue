<template>
  <div class="quiz-timer">
    <!-- Timer Display -->
    <div class="flex items-center justify-center mb-4">
      <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2">
        <div class="flex items-center space-x-2">
          <SvgIcon name="clock" class="w-5 h-5 text-white" />
          <span class="text-white font-mono text-lg">{{ formattedTime }}</span>
        </div>
      </div>
    </div>

    <!-- Timer Progress Bar -->
    <div class="w-full bg-white/10 rounded-full h-2 mb-4">
      <div
        class="h-2 rounded-full transition-all duration-1000 ease-linear"
        :class="getTimerBarClass"
        :style="{ width: `${timePercentage}%` }"
      />
    </div>

    <!-- Time Warning -->
    <div v-if="showWarning" class="text-center mb-4">
      <p class="text-yellow-400 text-sm font-medium animate-pulse">
        ⚠️ Less than 5 minutes remaining!
      </p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'

const props = defineProps({
  timeRemaining: {
    type: Number,
    required: true
  },
  totalTime: {
    type: Number,
    required: true
  }
})

// Computed properties
const formattedTime = computed(() => {
  const minutes = Math.floor(props.timeRemaining / 60)
  const seconds = props.timeRemaining % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

const timePercentage = computed(() => {
  if (props.totalTime === 0) return 0
  return Math.max(0, (props.timeRemaining / props.totalTime) * 100)
})

const showWarning = computed(() => {
  return props.timeRemaining <= 300 && props.timeRemaining > 0 // 5 minutes
})

const getTimerBarClass = computed(() => {
  const percentage = timePercentage.value
  if (percentage > 50) {
    return 'bg-gradient-to-r from-green-500 to-green-400'
  } else if (percentage > 25) {
    return 'bg-gradient-to-r from-yellow-500 to-yellow-400'
  } else {
    return 'bg-gradient-to-r from-red-500 to-red-400 animate-pulse'
  }
})
</script>

<style scoped>
.quiz-timer {
  @apply w-full;
}
</style>
