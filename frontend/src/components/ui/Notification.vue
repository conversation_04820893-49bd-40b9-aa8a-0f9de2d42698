<template>
  <div
    v-if="show"
    class="fixed bottom-20 left-4 z-50 max-w-sm transform transition-all duration-500"
    :class="[
      isVisible ? 'translate-x-0 opacity-100' : 'translate-x-[-100%] opacity-0',
      'animate-slide-in',
    ]"
  >
    <div
      role="alert"
      class="bg-green-100 dark:bg-green-900 border-l-4 border-green-500 dark:border-green-700 text-green-900 dark:text-green-100 p-4 rounded-lg flex items-center transition duration-300 ease-in-out hover:bg-green-200 dark:hover:bg-green-800 transform hover:scale-105 shadow-lg"
    >
      <svg
        stroke="currentColor"
        viewBox="0 0 24 24"
        fill="none"
        class="h-6 w-6 flex-shrink-0 mr-3 text-green-600"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          stroke-width="2"
          stroke-linejoin="round"
          stroke-linecap="round"
        ></path>
      </svg>
      <div>
        <p class="font-semibold">{{ title }}</p>
        <p class="text-sm">{{ message }}</p>
        <div v-if="actionText" class="mt-2">
          <button
            @click="onAction"
            class="text-green-700 dark:text-green-300 font-medium text-sm hover:underline"
          >
            {{ actionText }} →
          </button>
        </div>
      </div>
      <button
        @click="close"
        class="ml-auto -mx-1.5 -my-1.5 bg-green-50 dark:bg-green-800 text-green-500 rounded-lg p-1.5 hover:bg-green-200 dark:hover:bg-green-700"
      >
        <span class="sr-only">Close</span>
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Success",
  },
  message: {
    type: String,
    required: true,
  },
  duration: {
    type: Number,
    default: 7000, // timer of pop up visible
  },
  actionText: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["action", "close"]);

const show = ref(true);
const isVisible = ref(false);
let timer = null;

const close = () => {
  isVisible.value = false;
  setTimeout(() => {
    show.value = false;
    emit("close");
    // Refresh the page after notification closes
    window.location.reload();
  }, 500);
};

const onAction = () => {
  emit("action");
  close();
};

onMounted(() => {
  // Show with animation after a brief delay
  setTimeout(() => {
    isVisible.value = true;
  }, 100);

  // Auto-close after duration
  if (props.duration > 0) {
    timer = setTimeout(close, props.duration);
  }
});

onBeforeUnmount(() => {
  if (timer) clearTimeout(timer);
});
</script>

<style scoped>
@keyframes slide-in {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.5s ease-out forwards;
}
</style>
